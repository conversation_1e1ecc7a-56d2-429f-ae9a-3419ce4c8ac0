"""
Utility functions for symbol processing and conversion.
Consolidates common symbol operations to avoid duplication.
"""

import re
import logging
from typing import Optional
from src.database.models import MarketType

logger = logging.getLogger(__name__)


class SymbolUtils:
    """Utility class for common symbol operations."""
    
    @staticmethod
    def extract_underlying_symbol(symbol: str, market_type: Optional[MarketType] = None) -> str:
        """
        Extract underlying symbol from any symbol format.
        
        Args:
            symbol: Symbol to extract underlying from (NSE:RELIANCE-EQ, NIFTY25JUL25000CE, etc.)
            market_type: Optional market type hint for better extraction
            
        Returns:
            Underlying symbol (e.g., RELIANCE, NIFTY)
        """
        # Remove NSE: prefix if present
        clean_symbol = symbol.replace('NSE:', '').strip().upper()
        
        # Handle different market types
        if market_type == MarketType.EQUITY:
            return clean_symbol.replace('-EQ', '')
            
        elif market_type == MarketType.INDEX:
            return clean_symbol.replace('-INDEX', '')
            
        elif market_type == MarketType.FUTURES:
            # Extract underlying from futures format (e.g., RELIANCE25JULFUT -> RELIANCE)
            match = re.match(r'^([A-Z&-]+?)(?:\d{2}[A-Z]{3})?FUT$', clean_symbol)
            if match:
                return match.group(1)
            # Fallback: remove common suffixes
            for suffix in ['FUT', '25JULFUT', '25AUGFUT', '25SEPFUT']:
                if clean_symbol.endswith(suffix):
                    return clean_symbol[:-len(suffix)]
            return clean_symbol
            
        elif market_type == MarketType.OPTIONS:
            # Extract underlying from options format (e.g., NIFTY25JUL25000CE -> NIFTY)
            # Handle both monthly and weekly options with decimal strikes
            monthly_match = re.match(r'^([A-Z&-]+?)(\d{2}[A-Z]{3})(\d+(?:\.\d+)?)[CP]E$', clean_symbol)
            if monthly_match:
                return monthly_match.group(1)
                
            weekly_match = re.match(r'^([A-Z&-]+?)(\d{4})(\d+(?:\.\d+)?)[CP]E$', clean_symbol)
            if weekly_match:
                return weekly_match.group(1)
                
            # Fallback: remove common suffixes
            for suffix in ['CE', 'PE']:
                if clean_symbol.endswith(suffix):
                    base = clean_symbol[:-2]
                    # Remove strike price (including decimals)
                    base = re.sub(r'\d+(?:\.\d+)?$', '', base)
                    # Remove expiry patterns
                    base = re.sub(r'\d{2}[A-Z]{3}$', '', base)  # 25JUL
                    base = re.sub(r'\d{4}$', '', base)  # 2507
                    return base
            return clean_symbol
            
        else:
            # Auto-detect market type and extract accordingly
            if clean_symbol.endswith('-EQ'):
                return clean_symbol.replace('-EQ', '')
            elif clean_symbol.endswith('-INDEX'):
                return clean_symbol.replace('-INDEX', '')
            elif 'FUT' in clean_symbol:
                match = re.match(r'^([A-Z&-]+?)(?:\d{2}[A-Z]{3})?FUT$', clean_symbol)
                if match:
                    return match.group(1)
                return clean_symbol.replace('FUT', '')
            elif clean_symbol.endswith(('CE', 'PE')):
                # Options pattern
                monthly_match = re.match(r'^([A-Z&-]+?)(\d{2}[A-Z]{3})(\d+(?:\.\d+)?)[CP]E$', clean_symbol)
                if monthly_match:
                    return monthly_match.group(1)
                    
                weekly_match = re.match(r'^([A-Z&-]+?)(\d{4})(\d+(?:\.\d+)?)[CP]E$', clean_symbol)
                if weekly_match:
                    return weekly_match.group(1)
                    
                # Fallback for options
                for suffix in ['CE', 'PE']:
                    if clean_symbol.endswith(suffix):
                        base = clean_symbol[:-2]
                        base = re.sub(r'\d+(?:\.\d+)?$', '', base)
                        base = re.sub(r'\d{2}[A-Z]{3}$', '', base)
                        base = re.sub(r'\d{4}$', '', base)
                        return base
            
            # Default: return as-is
            return clean_symbol
    
    @staticmethod
    def clean_underlying_symbol(underlying_symbol: str) -> str:
        """
        Clean underlying symbol to remove malformed parts from option symbol extraction.
        
        Examples:
            ABCAPITAL25JUL207. -> ABCAPITAL
            MOTHERSON25JUL91 -> MOTHERSON
            RELIANCE -> RELIANCE (unchanged)
        """
        # Remove any trailing dots
        symbol = underlying_symbol.rstrip('.')
        
        # Pattern to match and remove option-like suffixes: SYMBOL25JUL123, SYMBOL25AUG456, etc.
        # This handles cases where option symbols were incorrectly extracted as underlying
        symbol = re.sub(r'\d{2}[A-Z]{3}\d+$', '', symbol)  # Remove 25JUL123 pattern
        symbol = re.sub(r'\d{4}\d+$', '', symbol)  # Remove 2507123 pattern (weekly)
        
        # If the symbol is empty after cleaning, return the original
        if not symbol.strip():
            return underlying_symbol
            
        return symbol.strip()
    
    @staticmethod
    def format_fyers_symbol(underlying: str, market_type: MarketType, 
                          expiry_date: Optional[str] = None, 
                          strike_price: Optional[float] = None,
                          option_type: Optional[str] = None) -> str:
        """
        Format symbol to Fyers format based on market type.
        
        Args:
            underlying: Underlying symbol (e.g., RELIANCE, NIFTY)
            market_type: Market type enum
            expiry_date: Expiry date for futures/options (YYYY-MM-DD format)
            strike_price: Strike price for options
            option_type: Option type ('CE' or 'PE') for options
            
        Returns:
            Properly formatted Fyers symbol
        """
        if market_type == MarketType.EQUITY:
            return f"NSE:{underlying}-EQ"
            
        elif market_type == MarketType.INDEX:
            return f"NSE:{underlying}-INDEX"
            
        elif market_type == MarketType.FUTURES:
            if expiry_date:
                # Format expiry for futures (e.g., 25JUL)
                from datetime import datetime
                try:
                    date_obj = datetime.strptime(expiry_date, '%Y-%m-%d')
                    year_month = date_obj.strftime('%y%b').upper()
                    return f"NSE:{underlying}{year_month}FUT"
                except ValueError:
                    logger.warning(f"Invalid expiry date format: {expiry_date}")
            return f"NSE:{underlying}FUT"
            
        elif market_type == MarketType.OPTIONS:
            if expiry_date and strike_price and option_type:
                # Format expiry for options (e.g., 25JUL)
                from datetime import datetime
                try:
                    date_obj = datetime.strptime(expiry_date, '%Y-%m-%d')
                    year_month = date_obj.strftime('%y%b').upper()
                    # Format strike price (remove decimal if it's a whole number)
                    strike_str = str(int(strike_price)) if strike_price.is_integer() else str(strike_price)
                    return f"NSE:{underlying}{year_month}{strike_str}{option_type}"
                except ValueError:
                    logger.warning(f"Invalid expiry date format: {expiry_date}")
            return f"NSE:{underlying}"
            
        else:
            return f"NSE:{underlying}"
    
    @staticmethod
    def is_valid_symbol_format(symbol: str, market_type: MarketType) -> bool:
        """
        Validate if symbol matches expected format for market type.
        
        Args:
            symbol: Symbol to validate
            market_type: Expected market type
            
        Returns:
            True if symbol format is valid for the market type
        """
        clean_symbol = symbol.replace('NSE:', '').strip().upper()
        
        if market_type == MarketType.EQUITY:
            return clean_symbol.endswith('-EQ')
            
        elif market_type == MarketType.INDEX:
            return clean_symbol.endswith('-INDEX')
            
        elif market_type == MarketType.FUTURES:
            return 'FUT' in clean_symbol
            
        elif market_type == MarketType.OPTIONS:
            return clean_symbol.endswith(('CE', 'PE'))
            
        return False
