"""
Test suite for main.py date range functionality.
Tests that main.py properly handles start-date and end-date parameters.
"""

import pytest
import sys
import os
from unittest.mock import patch, MagicMock
from datetime import datetime, timedelta

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from src.handlers.data_fetch_handler import DataFetchHandler


class TestMainDateRangeFunctionality:
    """Test main.py date range functionality."""
    
    def test_parse_date_arguments_with_valid_dates(self):
        """Test parsing of valid start and end dates."""
        handler = DataFetchHandler()
        
        # Mock args with valid dates
        args = MagicMock()
        args.start_date = "2025-07-01"
        args.end_date = "2025-07-10"
        args.days = 10
        
        start_date, end_date = handler._parse_date_arguments(args)
        
        assert start_date.date() == datetime(2025, 7, 1).date()
        assert end_date.date() == datetime(2025, 7, 10).date()
        assert start_date.hour == 0
        assert end_date.hour == 23
    
    def test_parse_date_arguments_with_future_dates(self):
        """Test parsing of future dates (should be adjusted to today)."""
        handler = DataFetchHandler()
        
        # Mock args with future dates
        future_date = (datetime.now() + timedelta(days=30)).strftime("%Y-%m-%d")
        args = MagicMock()
        args.start_date = "2025-07-01"
        args.end_date = future_date
        args.days = 10
        
        start_date, end_date = handler._parse_date_arguments(args)
        
        # End date should be adjusted to today
        assert end_date.date() <= datetime.now().date()
    
    def test_parse_date_arguments_fallback_to_days(self):
        """Test fallback to days argument when dates not provided."""
        handler = DataFetchHandler()
        
        # Mock args without dates
        args = MagicMock()
        args.start_date = None
        args.end_date = None
        args.days = 5
        
        start_date, end_date = handler._parse_date_arguments(args)
        
        # Should use days argument
        expected_days = (end_date - start_date).days
        assert expected_days == 5
        assert end_date.date() == datetime.now().date()
    
    def test_parse_date_arguments_invalid_format(self):
        """Test handling of invalid date formats."""
        handler = DataFetchHandler()
        
        # Mock args with invalid date format
        args = MagicMock()
        args.start_date = "2025/07/01"  # Wrong format
        args.end_date = "2025-07-10"
        args.days = 10
        
        with pytest.raises(ValueError):
            handler._parse_date_arguments(args)
    
    def test_parse_date_arguments_start_after_end(self):
        """Test handling when start date is after end date."""
        handler = DataFetchHandler()
        
        # Mock args with start date after end date
        args = MagicMock()
        args.start_date = "2025-07-10"
        args.end_date = "2025-07-01"
        args.days = 10
        
        with pytest.raises(ValueError):
            handler._parse_date_arguments(args)
    
    def test_validate_date_range_warnings(self):
        """Test date range validation warnings."""
        handler = DataFetchHandler()
        
        # Test future end date warning
        future_date = datetime.now() + timedelta(days=1)
        past_date = datetime.now() - timedelta(days=1)
        
        # Should return True but log warnings
        result = handler._validate_date_range(past_date, future_date)
        assert result is True
        
        # Test very old start date warning
        very_old_date = datetime.now() - timedelta(days=6*365)  # 6 years ago
        recent_date = datetime.now() - timedelta(days=1)
        
        result = handler._validate_date_range(very_old_date, recent_date)
        assert result is True
    
    @patch('src.handlers.data_fetch_handler.CLIOperations')
    def test_handle_bulk_all_markets_with_dates(self, mock_cli_ops):
        """Test bulk all markets operation with date range."""
        handler = DataFetchHandler()
        
        # Mock CLI operations
        mock_cli_instance = MagicMock()
        mock_cli_ops.return_value = mock_cli_instance
        mock_cli_instance.process_symbols_with_resume_and_dates.return_value = {
            'success': True,
            'successful_symbols': 10,
            'failed_symbols': 0
        }
        
        # Mock NSE symbol processor
        with patch('src.handlers.data_fetch_handler.NSESymbolProcessor') as mock_processor:
            mock_processor_instance = MagicMock()
            mock_processor.return_value = mock_processor_instance
            mock_processor_instance.process_nse_files.return_value = {
                'EQUITY': True,
                'INDEX': True,
                'FUTURES': True,
                'OPTIONS': True
            }
            mock_processor_instance.get_sample_symbols_by_type.return_value = {
                'EQUITY': 'RELIANCE',
                'INDEX': 'NIFTY50',
                'FUTURES': 'RELIANCE',
                'OPTIONS': 'RELIANCE'
            }
            
            # Mock bulk service
            with patch('src.handlers.data_fetch_handler.BulkDataService') as mock_bulk_service:
                mock_bulk_instance = MagicMock()
                mock_bulk_service.return_value = mock_bulk_instance
                
                # Create a mock coroutine for the async method
                async def mock_populate_all_market_types_with_dates(*args, **kwargs):
                    return {
                        'EQUITY': True,
                        'INDEX': True,
                        'FUTURES': True,
                        'OPTIONS': True
                    }
                
                mock_bulk_instance.populate_all_market_types_with_dates = mock_populate_all_market_types_with_dates
                
                # Mock args with date range
                args = MagicMock()
                args.start_date = "2025-07-01"
                args.end_date = "2025-07-10"
                args.days = 10
                args.symbols = None
                
                # Test the handler
                result = handler._handle_bulk_all_markets(args)
                
                # Should succeed
                assert result is True
    
    @patch('src.handlers.data_fetch_handler.CLIOperations')
    def test_handle_auto_all_symbols_with_dates(self, mock_cli_ops):
        """Test auto all symbols operation with date range."""
        handler = DataFetchHandler()
        
        # Mock CLI operations
        mock_cli_instance = MagicMock()
        mock_cli_ops.return_value = mock_cli_instance
        mock_cli_instance.process_symbols_with_resume_and_dates.return_value = {
            'success': True,
            'successful_symbols': 50,
            'failed_symbols': 5
        }
        
        # Mock args with date range and market type
        args = MagicMock()
        args.start_date = "2025-07-01"
        args.end_date = "2025-07-10"
        args.days = 10
        args.market_type = "EQUITY"
        args.resume_from = None
        args.limit = None
        
        # Test the handler
        result = handler._handle_auto_all_symbols(args)
        
        # Should succeed
        assert result is True
        
        # Verify CLI operations was called with correct date range
        mock_cli_instance.process_symbols_with_resume_and_dates.assert_called_once()
        call_args = mock_cli_instance.process_symbols_with_resume_and_dates.call_args
        
        # Check that start_date and end_date were passed
        assert 'start_date' in call_args.kwargs
        assert 'end_date' in call_args.kwargs
        assert call_args.kwargs['start_date'].date() == datetime(2025, 7, 1).date()
        assert call_args.kwargs['end_date'].date() == datetime(2025, 7, 10).date()


class TestCommandLineIntegration:
    """Test command line integration with date parameters."""
    
    def test_argument_parser_has_date_options(self):
        """Test that argument parser includes date options."""
        # Import main module
        import main
        
        parser = main.create_argument_parser()
        
        # Check that date arguments are present
        actions = {action.dest: action for action in parser._actions}
        
        assert 'start_date' in actions
        assert 'end_date' in actions
        assert 'days' in actions
        
        # Check default values
        assert actions['days'].default == 10
    
    @patch('src.handlers.command_dispatcher.CommandDispatcher')
    def test_main_function_with_date_args(self, mock_dispatcher):
        """Test main function with date arguments."""
        import main
        
        # Mock dispatcher
        mock_dispatcher_instance = MagicMock()
        mock_dispatcher.return_value = mock_dispatcher_instance
        mock_dispatcher_instance.dispatch.return_value = True
        
        # Mock sys.argv with date arguments
        test_args = [
            'main.py',
            '--bulk-all-markets',
            '--start-date', '2025-07-01',
            '--end-date', '2025-07-10'
        ]
        
        with patch('sys.argv', test_args):
            result = main.main()
            
            # Should succeed
            assert result is True
            
            # Verify dispatcher was called
            mock_dispatcher_instance.dispatch.assert_called_once()
            
            # Check that args were parsed correctly
            call_args = mock_dispatcher_instance.dispatch.call_args[0][0]
            assert call_args.start_date == '2025-07-01'
            assert call_args.end_date == '2025-07-10'
            assert call_args.bulk_all_markets is True


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
