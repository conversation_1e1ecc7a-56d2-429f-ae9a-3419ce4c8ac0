# Scalable Stock Market Trading Platform Configuration

# General Settings
general:
  env_path: '.env'
  output_dir: 'reports'
  fyers_api_url: ['https://public.fyers.in/sym_details/NSE_CM.csv', 'https://public.fyers.in/sym_details/NSE_FO.csv']

# Database Configuration
database:
  host: 'localhost'
  port: 5432
  name: 'nse_db'
  user: 'postgres'
  password: 'admin'  # Updated with actual password
  timescale_enabled: true

# Market Types
market_types:
  - EQUITY
  - INDEX
  - FUTURES
  - OPTIONS

# Trading Symbols (Initial set for development)
symbols:
  - 'NIFTY50'
  - 'NIFTY'
  - 'BANKNIFTY'
  - 'FINNIFTY'
  - 'RELIANCE'
  
# Timeframe Settings
timeframe:           # When you change 60 mins to 1D, you need to change days_to_fetch to 30, otherwise do not change it.
  interval: 1        # 1 minute for historical data storage
  days_to_fetch: 90  # 3 months (90 days) for comprehensive historical data

# Rate Limiting Settings / DO NOT CHANGE this, otherwise it will be a problem and slow down the process.
rate_limit:
  min_delay_seconds: 0.1   # Minimum delay between API requests (seconds) - optimized for performance
  max_retries: 1           # Number of retries on 429 error - increased for reliability
  retry_backoff: 2.0       # Base seconds for exponential backoff on retry
