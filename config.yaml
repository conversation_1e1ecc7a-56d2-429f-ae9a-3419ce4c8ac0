# Scalable Stock Market Trading Platform Configuration

# General Settings
general:
  env_path: '.env'
  output_dir: 'reports'
  fyers_api_url: ['https://public.fyers.in/sym_details/NSE_CM.csv', 'https://public.fyers.in/sym_details/NSE_FO.csv']

# Database Configuration
database:
  host: 'localhost'
  port: 5432
  name: 'nse_db'
  user: 'postgres'
  password: 'admin'  # Updated with actual password
  timescale_enabled: true

# Market Types
market_types:
  - EQUITY
  - INDEX
  - FUTURES
  - OPTIONS

# Trading Symbols (Initial set for development)
symbols:
  - 'NIFTY50'
  - 'NIFTY'
  - 'BANKNIFTY'
  - 'FINNIFTY'
  - 'RELIANCE'
  
# Timeframe Settings
timeframe:           # When you change 60 mins to 1D, you need to change days_to_fetch to 30, otherwise do not change it.
  interval: 1        # 1 minute for historical data storage
  days_to_fetch: 90  # 3 months (90 days) for comprehensive historical data

# HIGH-PERFORMANCE Rate Limiting Settings - Optimized for OPTIONS processing
rate_limit:
  min_delay_seconds: 0.05  # Reduced delay for better throughput (was 0.1)
  max_retries: 1           # Keep retries low to avoid wasting time on no_data symbols
  retry_backoff: 1.5       # Reduced backoff time (was 2.0)

# HIGH-PERFORMANCE Processing Settings
performance:
  parallel_processing: true
  max_concurrent_batches: 3    # Process 3 batches concurrently
  batch_size: 50              # Increased from 10 for better throughput
  enable_smart_filtering: true # Skip expired and illiquid options
  enable_auto_resume: true     # Automatic resume from last position
  export_symbol_csv: true     # Export planned symbols to CSV
