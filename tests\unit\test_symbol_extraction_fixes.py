"""
Test suite for symbol extraction fixes.
Tests the fixes for issues like ABCAPITAL25JUL207. and MOTHERSON25JUL91.
"""

import pytest
import sys
import os

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from src.core.symbol_utils import SymbolUtils
from src.database.models import MarketType


class TestSymbolExtractionFixes:
    """Test symbol extraction fixes for malformed symbols."""
    
    def test_extract_underlying_from_decimal_strike_options(self):
        """Test extraction of underlying from options with decimal strikes."""
        # Test cases that were causing issues
        test_cases = [
            ("ABCAPITAL25JUL207.5CE", "ABCAPITAL"),
            ("MOTHERSON25JUL91.65PE", "MOTHERSON"),
            ("RELIANCE25JUL2500.50CE", "RELIANCE"),
            ("NIFTY25JUL25000.25PE", "NIFTY"),
            ("BANKNIFTY25JUL50000.75CE", "BANKNIFTY"),
        ]
        
        for symbol, expected_underlying in test_cases:
            result = SymbolUtils.extract_underlying_symbol(symbol, MarketType.OPTIONS)
            assert result == expected_underlying, f"Failed for {symbol}: expected {expected_underlying}, got {result}"
    
    def test_clean_underlying_symbol_fixes(self):
        """Test cleaning of malformed underlying symbols."""
        # Test cases that were causing issues in logs
        test_cases = [
            ("ABCAPITAL25JUL207.", "ABCAPITAL"),
            ("MOTHERSON25JUL91", "MOTHERSON"),
            ("RELIANCE25JUL2500", "RELIANCE"),
            ("NIFTY25JUL25000", "NIFTY"),
            ("BANKNIFTY25072550000", "BANKNIFTY"),  # Weekly format
            ("NORMAL_SYMBOL", "NORMAL_SYMBOL"),  # Should remain unchanged
        ]
        
        for malformed_symbol, expected_clean in test_cases:
            result = SymbolUtils.clean_underlying_symbol(malformed_symbol)
            assert result == expected_clean, f"Failed for {malformed_symbol}: expected {expected_clean}, got {result}"
    
    def test_extract_underlying_from_various_formats(self):
        """Test underlying extraction from various symbol formats."""
        test_cases = [
            # Equity symbols
            ("NSE:RELIANCE-EQ", MarketType.EQUITY, "RELIANCE"),
            ("RELIANCE-EQ", MarketType.EQUITY, "RELIANCE"),
            
            # Index symbols
            ("NSE:NIFTY50-INDEX", MarketType.INDEX, "NIFTY50"),
            ("NIFTY50-INDEX", MarketType.INDEX, "NIFTY50"),
            
            # Futures symbols
            ("NSE:RELIANCE25JULFUT", MarketType.FUTURES, "RELIANCE"),
            ("RELIANCE25JULFUT", MarketType.FUTURES, "RELIANCE"),
            
            # Options symbols - monthly
            ("NSE:RELIANCE25JUL2500CE", MarketType.OPTIONS, "RELIANCE"),
            ("RELIANCE25JUL2500CE", MarketType.OPTIONS, "RELIANCE"),
            ("NIFTY25JUL25000PE", MarketType.OPTIONS, "NIFTY"),
            
            # Options symbols - weekly
            ("NSE:NIFTY2572425050CE", MarketType.OPTIONS, "NIFTY"),
            ("NIFTY2572425050CE", MarketType.OPTIONS, "NIFTY"),
            
            # Options symbols with decimal strikes (the problematic ones)
            ("ABCAPITAL25JUL207.5CE", MarketType.OPTIONS, "ABCAPITAL"),
            ("MOTHERSON25JUL91.65PE", MarketType.OPTIONS, "MOTHERSON"),
        ]
        
        for symbol, market_type, expected_underlying in test_cases:
            result = SymbolUtils.extract_underlying_symbol(symbol, market_type)
            assert result == expected_underlying, f"Failed for {symbol} ({market_type}): expected {expected_underlying}, got {result}"
    
    def test_auto_detect_market_type_extraction(self):
        """Test underlying extraction with auto-detection of market type."""
        test_cases = [
            ("RELIANCE-EQ", "RELIANCE"),
            ("NIFTY50-INDEX", "NIFTY50"),
            ("RELIANCE25JULFUT", "RELIANCE"),
            ("RELIANCE25JUL2500CE", "RELIANCE"),
            ("NIFTY25JUL25000PE", "NIFTY"),
            ("ABCAPITAL25JUL207.5CE", "ABCAPITAL"),  # Problematic case
            ("MOTHERSON25JUL91.65PE", "MOTHERSON"),  # Problematic case
        ]
        
        for symbol, expected_underlying in test_cases:
            result = SymbolUtils.extract_underlying_symbol(symbol)  # No market type specified
            assert result == expected_underlying, f"Failed for {symbol}: expected {expected_underlying}, got {result}"
    
    def test_format_fyers_symbol(self):
        """Test formatting symbols to Fyers format."""
        from datetime import datetime
        
        test_cases = [
            # Equity
            ("RELIANCE", MarketType.EQUITY, None, None, None, "NSE:RELIANCE-EQ"),
            
            # Index
            ("NIFTY50", MarketType.INDEX, None, None, None, "NSE:NIFTY50-INDEX"),
            
            # Futures
            ("RELIANCE", MarketType.FUTURES, "2025-07-31", None, None, "NSE:RELIANCE25JULFUT"),
            
            # Options
            ("RELIANCE", MarketType.OPTIONS, "2025-07-31", 2500.0, "CE", "NSE:RELIANCE25JUL2500CE"),
            ("NIFTY", MarketType.OPTIONS, "2025-07-31", 25000.5, "PE", "NSE:NIFTY25JUL25000.5PE"),
        ]
        
        for underlying, market_type, expiry_date, strike_price, option_type, expected_symbol in test_cases:
            result = SymbolUtils.format_fyers_symbol(underlying, market_type, expiry_date, strike_price, option_type)
            assert result == expected_symbol, f"Failed for {underlying} ({market_type}): expected {expected_symbol}, got {result}"
    
    def test_symbol_validation(self):
        """Test symbol format validation."""
        test_cases = [
            # Valid cases
            ("RELIANCE-EQ", MarketType.EQUITY, True),
            ("NIFTY50-INDEX", MarketType.INDEX, True),
            ("RELIANCE25JULFUT", MarketType.FUTURES, True),
            ("RELIANCE25JUL2500CE", MarketType.OPTIONS, True),
            ("ABCAPITAL25JUL207.5CE", MarketType.OPTIONS, True),  # Should be valid
            
            # Invalid cases
            ("RELIANCE-EQ", MarketType.INDEX, False),  # Wrong market type
            ("NIFTY50-INDEX", MarketType.EQUITY, False),  # Wrong market type
            ("RELIANCE", MarketType.EQUITY, False),  # Missing -EQ suffix
            ("INVALID", MarketType.OPTIONS, False),  # Invalid options format
        ]
        
        for symbol, market_type, expected_valid in test_cases:
            result = SymbolUtils.is_valid_symbol_format(symbol, market_type)
            assert result == expected_valid, f"Failed for {symbol} ({market_type}): expected {expected_valid}, got {result}"


class TestSpotPriceServiceFixes:
    """Test spot price service fixes for malformed symbols."""
    
    def test_clean_underlying_symbol_integration(self):
        """Test that spot price service properly cleans malformed underlying symbols."""
        from src.services.spot_price_service import SpotPriceService
        
        service = SpotPriceService()
        
        # Test the problematic symbols that were appearing in logs
        test_cases = [
            ("ABCAPITAL25JUL207.", "ABCAPITAL"),
            ("MOTHERSON25JUL91", "MOTHERSON"),
            ("RELIANCE25JUL2500", "RELIANCE"),
        ]
        
        for malformed_symbol, expected_clean in test_cases:
            result = service._clean_underlying_symbol(malformed_symbol)
            assert result == expected_clean, f"Failed for {malformed_symbol}: expected {expected_clean}, got {result}"


class TestOptionsPrivoritizerFixes:
    """Test options prioritizer fixes for symbol extraction."""
    
    def test_extract_underlying_symbol_integration(self):
        """Test that options prioritizer properly extracts underlying symbols."""
        from src.services.options_prioritizer import OptionsPrioritizer
        
        prioritizer = OptionsPrioritizer()
        
        # Test the problematic symbols
        test_cases = [
            ("NSE:ABCAPITAL25JUL207.5CE", "ABCAPITAL"),
            ("NSE:MOTHERSON25JUL91.65PE", "MOTHERSON"),
            ("NSE:RELIANCE25JUL2500CE", "RELIANCE"),
            ("NSE:NIFTY25JUL25000PE", "NIFTY"),
        ]
        
        for symbol, expected_underlying in test_cases:
            result = prioritizer._extract_underlying_symbol(symbol)
            assert result == expected_underlying, f"Failed for {symbol}: expected {expected_underlying}, got {result}"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
