#!/usr/bin/env python3
"""
Test script to verify the enhanced symbol processing implementation.
Tests the 5 pattern-based filtering and UPSERT functionality.
"""

import sys
import os
import logging
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.core.nse_symbol_helpers import NSESymbolHelpers
import pandas as pd

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_pattern_matching():
    """Test the 5 specified pattern matching."""
    logger.info("🧪 Testing pattern matching for the 5 specified patterns...")
    
    helpers = NSESymbolHelpers()
    
    # Test symbols for each pattern
    test_symbols = {
        'EQUITY': [
            'RELIANCE-EQ',
            'TCS-EQ', 
            'HDFCBANK-EQ',
            'M&M-EQ',
            'BAJAJ-AUTO-EQ'
        ],
        'INDEX': [
            'NIFTY50-INDEX',
            'BANKNIFTY-INDEX',
            'NIFTYIT-INDEX',
            'FINNIFTY-INDEX',
            # Space-containing INDEX symbols (newly supported)
            'NIFTY100 EQL WGT-INDEX',
            'NIFTY50 EQL WGT-INDEX',
            'HANGSENG BEES-NAV-INDEX',
            'NIFTY100 LOWVOL30-INDEX'
        ],
        'FUTURES': [
            'RELIANCE25JULFUT',
            'NIFTY25JULFUT',
            'BANKNIFTY25JULFUT',
            'TCS25AUGFUT'
        ],
        'OPTIONS_MONTHLY': [
            'RELIANCE25JUL2500CE',
            'RELIANCE25JUL2500PE',
            'NIFTY25JUL25000CE',
            'BANKNIFTY25JUL50000PE',
            'M&M25JUL1500CE',
            'BAJAJ-AUTO25JUL5900CE'
        ],
        'OPTIONS_WEEKLY': [
            'NIFTY2572425050CE',
            'NIFTY2572425050PE',
            'BANKNIFTY2572450000CE',
            'FINNIFTY2572420000PE'
        ]
    }
    
    # Test invalid symbols that should be skipped
    invalid_symbols = [
        'INVALID-SYMBOL',
        'RELIANCE',  # Missing -EQ
        'NIFTY50',   # Missing -INDEX
        'RELIANCE25JUL',  # Incomplete futures
        'RELIANCE25JUL2500',  # Missing CE/PE
        'RANDOM123XYZ'
    ]
    
    results = {
        'EQUITY': 0,
        'INDEX': 0,
        'FUTURES': 0,
        'OPTIONS': 0,
        'INVALID': 0
    }
    
    # Test valid symbols
    for expected_type, symbols in test_symbols.items():
        logger.info(f"\n📋 Testing {expected_type} patterns:")
        for symbol in symbols:
            symbol_info = helpers.extract_symbol_info(symbol)
            detected_type = symbol_info['market_type']
            
            if expected_type == 'OPTIONS_MONTHLY' or expected_type == 'OPTIONS_WEEKLY':
                expected_type_check = 'OPTIONS'
            else:
                expected_type_check = expected_type
                
            if detected_type == expected_type_check:
                logger.info(f"  ✅ {symbol} -> {detected_type}")
                results[detected_type] += 1
            else:
                logger.error(f"  ❌ {symbol} -> Expected: {expected_type_check}, Got: {detected_type}")
    
    # Test invalid symbols
    logger.info(f"\n📋 Testing invalid symbols (should be skipped):")
    for symbol in invalid_symbols:
        symbol_info = helpers.extract_symbol_info(symbol)
        detected_type = symbol_info['market_type']
        
        if detected_type is None:
            logger.info(f"  ✅ {symbol} -> Correctly skipped")
            results['INVALID'] += 1
        else:
            logger.error(f"  ❌ {symbol} -> Should be skipped but got: {detected_type}")
    
    logger.info(f"\n📊 Pattern matching test results:")
    for pattern_type, count in results.items():
        logger.info(f"  {pattern_type}: {count} symbols")
    
    return results

def test_symbol_filtering():
    """Test symbol filtering with sample data."""
    logger.info("\n🧪 Testing symbol filtering with sample data...")
    
    helpers = NSESymbolHelpers()
    
    # Create sample DataFrame mimicking NSE CSV structure
    sample_data = [
        {'symbol_name': 'NSE:RELIANCE-EQ', 'company_name': 'Reliance Industries'},
        {'symbol_name': 'NSE:TCS-EQ', 'company_name': 'Tata Consultancy Services'},
        {'symbol_name': 'NSE:NIFTY50-INDEX', 'company_name': 'NIFTY 50'},
        {'symbol_name': 'NSE:RELIANCE25JULFUT', 'company_name': 'Reliance Jul Futures'},
        {'symbol_name': 'NSE:RELIANCE25JUL2500CE', 'company_name': 'Reliance Jul 2500 Call'},
        {'symbol_name': 'NSE:NIFTY2572425050CE', 'company_name': 'Nifty Weekly Call'},
        {'symbol_name': 'NSE:INVALID-SYMBOL', 'company_name': 'Invalid Symbol'},
        {'symbol_name': 'BSE:RELIANCE', 'company_name': 'BSE Reliance'},  # Non-NSE
    ]
    
    df = pd.DataFrame(sample_data)
    filtered_df = helpers.filter_relevant_symbols(df)
    
    logger.info(f"📊 Filtering results:")
    logger.info(f"  Input symbols: {len(df)}")
    logger.info(f"  Filtered symbols: {len(filtered_df)}")
    logger.info(f"  Expected filtered: 6 (excluding invalid and BSE symbols)")
    
    if len(filtered_df) == 6:
        logger.info("  ✅ Filtering test PASSED")
        return True
    else:
        logger.error("  ❌ Filtering test FAILED")
        return False

def main():
    """Run all tests."""
    logger.info("🚀 Starting enhanced symbol processing tests...")
    logger.info("=" * 60)
    
    # Test 1: Pattern matching
    pattern_results = test_pattern_matching()
    
    # Test 2: Symbol filtering
    filtering_success = test_symbol_filtering()
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("📋 TEST SUMMARY:")
    logger.info(f"  Pattern Matching: ✅ Completed")
    logger.info(f"  Symbol Filtering: {'✅ PASSED' if filtering_success else '❌ FAILED'}")
    
    total_patterns_tested = sum(pattern_results[k] for k in ['EQUITY', 'INDEX', 'FUTURES', 'OPTIONS'])
    logger.info(f"  Total Valid Patterns Tested: {total_patterns_tested}")
    logger.info(f"  Invalid Symbols Correctly Skipped: {pattern_results['INVALID']}")
    
    logger.info("\n💡 To test with real data, run:")
    logger.info("  python main.py --process-nse-symbols")
    logger.info("  python main.py --validate-data-integrity")

if __name__ == "__main__":
    main()
