"""
Trading Calendar Utilities for Indian Stock Market.
Handles expiry date calculations and trading holiday adjustments.
"""

import logging
from datetime import datetime, date, timedelta
from typing import Optional, Set
from calendar import monthrange

logger = logging.getLogger(__name__)


class TradingCalendar:
    """Trading calendar utilities for Indian stock market."""
    
    def __init__(self):
        """Initialize trading calendar with common Indian market holidays."""
        # Common Indian market holidays (this is a basic set - in production, 
        # you'd want to fetch this from a reliable source or maintain a comprehensive list)
        self.fixed_holidays = {
            # Republic Day
            (1, 26),
            # Independence Day  
            (8, 15),
            # <PERSON>
            (10, 2),
        }
        
        # Year-specific holidays (these change each year based on lunar calendar)
        # In production, you'd maintain a comprehensive database or API
        self.year_specific_holidays = {
            2024: {
                date(2024, 1, 22),  # <PERSON>
                date(2024, 3, 8),   # Holi
                date(2024, 3, 29),  # Good Friday
                date(2024, 4, 11),  # Eid ul-Fitr
                date(2024, 4, 17),  # <PERSON>
                date(2024, 5, 1),   # Maharashtra Day
                date(2024, 6, 17),  # Eid ul-<PERSON>ha
                date(2024, 8, 19),  # <PERSON><PERSON><PERSON>
                date(2024, 10, 31), # <PERSON><PERSON><PERSON>
                date(2024, 11, 1),  # <PERSON>wal<PERSON><PERSON>tipada
                date(2024, 11, 15), # <PERSON> <PERSON>k <PERSON><PERSON>
            },
            2025: {
                date(2025, 3, 14),  # Holi
                date(2025, 3, 31),  # Eid ul-Fitr
                date(2025, 4, 6),   # Ram Navami
                date(2025, 4, 18),  # Good Friday
                date(2025, 5, 1),   # Maharashtra Day
                date(2025, 6, 6),   # Eid ul-Adha
                date(2025, 8, 9),   # Raksha Bandhan
                date(2025, 10, 20), # Diwali Laxmi Puja
                date(2025, 11, 5),  # Guru Nanak Jayanti
            }
        }
    
    def is_trading_holiday(self, check_date: date) -> bool:
        """
        Check if a given date is a trading holiday.
        
        Args:
            check_date: Date to check
            
        Returns:
            True if it's a trading holiday, False otherwise
        """
        # Check if it's a weekend (Saturday=5, Sunday=6)
        if check_date.weekday() >= 5:
            return True
        
        # Check fixed holidays
        if (check_date.month, check_date.day) in self.fixed_holidays:
            return True
        
        # Check year-specific holidays
        year_holidays = self.year_specific_holidays.get(check_date.year, set())
        if check_date in year_holidays:
            return True
        
        return False
    
    def get_previous_trading_day(self, check_date: date) -> date:
        """
        Get the previous trading day before the given date.
        
        Args:
            check_date: Starting date
            
        Returns:
            Previous trading day
        """
        current_date = check_date - timedelta(days=1)
        
        # Keep going back until we find a trading day
        while self.is_trading_holiday(current_date):
            current_date -= timedelta(days=1)
        
        return current_date
    
    def get_last_thursday_of_month(self, year: int, month: int) -> date:
        """
        Get the last Thursday of a given month.
        
        Args:
            year: Year
            month: Month (1-12)
            
        Returns:
            Last Thursday of the month
        """
        # Get the last day of the month
        last_day_of_month = monthrange(year, month)[1]
        last_date = date(year, month, last_day_of_month)
        
        # Find the last Thursday
        # Thursday is weekday 3 (Monday=0, Tuesday=1, Wednesday=2, Thursday=3, ...)
        days_back = (last_date.weekday() - 3) % 7
        last_thursday = last_date - timedelta(days=days_back)
        
        return last_thursday
    
    def get_expiry_date_monthly(self, year: int, month: int) -> date:
        """
        Get the monthly expiry date (last Thursday of month, adjusted for holidays).
        
        Args:
            year: Year
            month: Month (1-12)
            
        Returns:
            Actual expiry date adjusted for trading holidays
        """
        last_thursday = self.get_last_thursday_of_month(year, month)
        
        # If last Thursday is a trading holiday, move to previous trading day
        if self.is_trading_holiday(last_thursday):
            expiry_date = self.get_previous_trading_day(last_thursday)
            logger.debug(f"Monthly expiry adjusted from {last_thursday} to {expiry_date} due to holiday")
            return expiry_date
        
        return last_thursday
    
    def get_expiry_date_weekly(self, year: int, month: int, day: int) -> Optional[date]:
        """
        Get the weekly expiry date (specific Thursday, adjusted for holidays).
        
        Args:
            year: Year
            month: Month (1-12)
            day: Day of the month
            
        Returns:
            Actual expiry date adjusted for trading holidays, or None if invalid date
        """
        try:
            # Validate the date
            expiry_date = date(year, month, day)
            
            # Check if it's actually a Thursday
            if expiry_date.weekday() != 3:  # Thursday is 3
                logger.warning(f"Weekly expiry date {expiry_date} is not a Thursday (weekday: {expiry_date.weekday()})")
                # For weekly options, the day should be a Thursday, but if it's not,
                # we'll still process it as the system might have different logic
            
            # If it's a trading holiday, move to previous trading day
            if self.is_trading_holiday(expiry_date):
                adjusted_date = self.get_previous_trading_day(expiry_date)
                logger.debug(f"Weekly expiry adjusted from {expiry_date} to {adjusted_date} due to holiday")
                return adjusted_date
            
            return expiry_date
            
        except ValueError as e:
            logger.error(f"Invalid weekly expiry date: {year}-{month}-{day}: {e}")
            return None
    
    def parse_expiry_from_symbol_info(self, symbol_info: dict, is_weekly: bool = False) -> Optional[date]:
        """
        Parse expiry date from symbol info dictionary.
        
        Args:
            symbol_info: Dictionary containing expiry information
            is_weekly: True for weekly options, False for monthly
            
        Returns:
            Parsed expiry date or None if parsing fails
        """
        try:
            if not symbol_info.get('expiry_year') or not symbol_info.get('expiry_month'):
                return None
            
            year = int(f"20{symbol_info['expiry_year']}")
            
            if is_weekly and symbol_info.get('expiry_day'):
                # Weekly pattern: specific day
                day = int(symbol_info['expiry_day'])
                
                # For weekly options, month might be a single digit
                if len(symbol_info['expiry_month']) == 1:
                    month = int(symbol_info['expiry_month'])
                else:
                    # Handle month abbreviations for weekly
                    month_map = {
                        'JAN': 1, 'FEB': 2, 'MAR': 3, 'APR': 4, 'MAY': 5, 'JUN': 6,
                        'JUL': 7, 'AUG': 8, 'SEP': 9, 'OCT': 10, 'NOV': 11, 'DEC': 12
                    }
                    month = month_map.get(symbol_info['expiry_month'].upper(), 1)
                
                return self.get_expiry_date_weekly(year, month, day)
            
            else:
                # Monthly pattern: last Thursday of month
                month_map = {
                    'JAN': 1, 'FEB': 2, 'MAR': 3, 'APR': 4, 'MAY': 5, 'JUN': 6,
                    'JUL': 7, 'AUG': 8, 'SEP': 9, 'OCT': 10, 'NOV': 11, 'DEC': 12
                }
                month = month_map.get(symbol_info['expiry_month'].upper(), 1)
                
                return self.get_expiry_date_monthly(year, month)
                
        except (ValueError, KeyError) as e:
            logger.error(f"Error parsing expiry from symbol info {symbol_info}: {e}")
            return None
    
    def add_holiday(self, holiday_date: date) -> None:
        """
        Add a new holiday to the calendar.
        
        Args:
            holiday_date: Date to add as holiday
        """
        year = holiday_date.year
        if year not in self.year_specific_holidays:
            self.year_specific_holidays[year] = set()
        
        self.year_specific_holidays[year].add(holiday_date)
        logger.info(f"Added holiday: {holiday_date}")
    
    def remove_holiday(self, holiday_date: date) -> None:
        """
        Remove a holiday from the calendar.
        
        Args:
            holiday_date: Date to remove from holidays
        """
        year = holiday_date.year
        if year in self.year_specific_holidays:
            self.year_specific_holidays[year].discard(holiday_date)
            logger.info(f"Removed holiday: {holiday_date}")


# Global instance for easy access
trading_calendar = TradingCalendar()


def get_monthly_expiry_date(year: int, month: int) -> date:
    """
    Convenience function to get monthly expiry date.
    
    Args:
        year: Year
        month: Month (1-12)
        
    Returns:
        Monthly expiry date (last Thursday, adjusted for holidays)
    """
    return trading_calendar.get_expiry_date_monthly(year, month)


def get_weekly_expiry_date(year: int, month: int, day: int) -> Optional[date]:
    """
    Convenience function to get weekly expiry date.
    
    Args:
        year: Year
        month: Month (1-12)
        day: Day
        
    Returns:
        Weekly expiry date (adjusted for holidays) or None if invalid
    """
    return trading_calendar.get_expiry_date_weekly(year, month, day)


if __name__ == "__main__":
    # Test the trading calendar
    print("🗓️  Testing Trading Calendar")
    print("=" * 50)
    
    # Test monthly expiry dates for 2024
    test_months = [(2024, 7), (2024, 8), (2024, 9), (2024, 10), (2024, 11), (2024, 12)]
    
    print("Monthly Expiry Dates:")
    for year, month in test_months:
        expiry = get_monthly_expiry_date(year, month)
        print(f"  {year}-{month:02d}: {expiry} ({expiry.strftime('%A')})")
    
    print("\nWeekly Expiry Dates:")
    # Test some weekly expiry dates
    weekly_tests = [(2024, 7, 25), (2024, 8, 1), (2024, 8, 8), (2024, 8, 15)]
    for year, month, day in weekly_tests:
        expiry = get_weekly_expiry_date(year, month, day)
        if expiry:
            print(f"  {year}-{month:02d}-{day:02d}: {expiry} ({expiry.strftime('%A')})")
        else:
            print(f"  {year}-{month:02d}-{day:02d}: Invalid date")
    
    print("\nHoliday Tests:")
    # Test some known holidays
    test_dates = [
        date(2024, 1, 26),  # Republic Day
        date(2024, 8, 15),  # Independence Day
        date(2024, 10, 2),  # Gandhi Jayanti
        date(2024, 3, 8),   # Holi
        date(2024, 7, 25),  # Regular Thursday
    ]
    
    for test_date in test_dates:
        is_holiday = trading_calendar.is_trading_holiday(test_date)
        print(f"  {test_date} ({test_date.strftime('%A')}): {'Holiday' if is_holiday else 'Trading Day'}")