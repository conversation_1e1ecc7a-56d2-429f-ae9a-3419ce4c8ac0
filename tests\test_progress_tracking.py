"""
Test suite for progress tracking improvements.
Tests that progress tracking shows proper completion percentages.
"""

import pytest
import sys
import os
from unittest.mock import patch, MagicMock, call
from datetime import datetime

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from src.services.bulk_data_service import BulkDataService
from src.services.options_prioritizer import OptionsPrioritizer
from src.database.models import MarketType


class TestBulkDataServiceProgressTracking:
    """Test progress tracking in bulk data service."""
    
    @patch('src.services.bulk_data_service.logger')
    @patch('src.services.bulk_data_service.FyersAuthService')
    @patch('src.services.bulk_data_service.get_db')
    def test_progress_logging_in_populate_historical_data(self, mock_get_db, mock_fyers_auth, mock_logger):
        """Test that progress is logged during historical data population."""
        # Mock database and auth service
        mock_db = MagicMock()
        mock_get_db.return_value = iter([mock_db])
        
        mock_auth_instance = MagicMock()
        mock_fyers_auth.return_value = mock_auth_instance
        mock_auth_instance.initialize.return_value = True
        
        # Create service instance
        service = BulkDataService()
        
        # Mock the internal methods to avoid actual API calls
        service._convert_to_fyers_symbol = MagicMock(side_effect=lambda x, y: f"NSE:{x}")
        service._fetch_historical_data = MagicMock(return_value=[])
        service._convert_to_db_format = MagicMock(return_value=[])
        service._store_data_batch = MagicMock(return_value=True)
        
        # Test with a list of symbols
        symbols = ['SYMBOL1', 'SYMBOL2', 'SYMBOL3', 'SYMBOL4', 'SYMBOL5', 
                  'SYMBOL6', 'SYMBOL7', 'SYMBOL8', 'SYMBOL9', 'SYMBOL10',
                  'SYMBOL11', 'SYMBOL12']  # 12 symbols to test progress logging
        
        # Call the method
        service.populate_historical_data(
            symbols=symbols,
            market_type=MarketType.EQUITY,
            days=5,
            interval="1m",
            exchange="NSE"
        )
        
        # Check that progress logging occurred
        progress_calls = [call for call in mock_logger.info.call_args_list 
                         if '📊 Processing EQUITY symbols:' in str(call)]
        
        # Should have progress logs (every 10th symbol or at end)
        assert len(progress_calls) >= 1, "Should have at least one progress log"
        
        # Check that percentage is included in progress logs
        progress_log_found = False
        for call in progress_calls:
            if '%' in str(call):
                progress_log_found = True
                break
        
        assert progress_log_found, "Progress logs should include percentage"


class TestOptionsPrivoritizerProgressTracking:
    """Test progress tracking in options prioritizer."""
    
    @patch('src.services.options_prioritizer.logger')
    @patch('src.services.options_prioritizer.get_db')
    @patch('src.services.options_prioritizer.SpotPriceService')
    def test_progress_logging_in_prioritize_symbols(self, mock_spot_service, mock_get_db, mock_logger):
        """Test that progress is logged during options prioritization."""
        # Mock database
        mock_db = MagicMock()
        mock_get_db.return_value = iter([mock_db])
        
        # Mock spot price service
        mock_spot_instance = MagicMock()
        mock_spot_service.return_value = mock_spot_instance
        mock_spot_instance.get_spot_price.return_value = 100.0
        
        # Create prioritizer instance
        prioritizer = OptionsPrioritizer()
        
        # Mock database query to return test symbols
        mock_symbols = []
        for i in range(150):  # Create enough symbols to trigger progress logging
            mock_symbol = MagicMock()
            mock_symbol.nse_symbol = f"NSE:SYMBOL{i}25JUL2500CE"
            mock_symbol.expiry_date = datetime(2025, 7, 31).date()
            mock_symbol.strike_price = 2500.0
            mock_symbol.option_type = "CE"
            mock_symbols.append(mock_symbol)
        
        mock_db.query.return_value.filter.return_value.all.return_value = mock_symbols
        
        # Call the method
        result = prioritizer.prioritize_symbols(
            market_type=MarketType.OPTIONS,
            limit=100,
            strike_range=0.2
        )
        
        # Check that progress logging occurred
        progress_calls = [call for call in mock_logger.info.call_args_list 
                         if '📊 OPTIONS Prioritization:' in str(call)]
        
        # Should have progress logs
        assert len(progress_calls) >= 1, "Should have at least one progress log"
        
        # Check that progress includes counts and percentages
        progress_log_found = False
        for call in progress_calls:
            call_str = str(call)
            if 'underlyings processed' in call_str and '%' in call_str:
                progress_log_found = True
                break
        
        assert progress_log_found, "Progress logs should include underlying counts and percentages"


class TestCLIOperationsProgressTracking:
    """Test progress tracking in CLI operations."""
    
    @patch('src.helpers.cli_operations.logger')
    @patch('src.helpers.cli_operations.BulkDataService')
    @patch('src.helpers.cli_operations.get_db')
    def test_progress_logging_in_batch_processing(self, mock_get_db, mock_bulk_service, mock_logger):
        """Test that progress is logged during batch processing."""
        from src.helpers.cli_operations import CLIOperations
        
        # Mock database
        mock_db = MagicMock()
        mock_get_db.return_value = iter([mock_db])
        
        # Mock bulk service
        mock_bulk_instance = MagicMock()
        mock_bulk_service.return_value = mock_bulk_instance
        mock_bulk_instance.populate_historical_data.return_value = True
        
        # Create CLI operations instance
        cli_ops = CLIOperations()
        
        # Mock database query to return test symbols
        mock_symbols = []
        for i in range(55):  # Create enough symbols to trigger batch progress logging
            mock_symbol = MagicMock()
            mock_symbol.nse_symbol = f"SYMBOL{i}-EQ"
            mock_symbols.append(mock_symbol)
        
        mock_db.query.return_value.filter.return_value.all.return_value = mock_symbols
        
        # Call the method
        result = cli_ops.process_symbols_with_resume_and_dates(
            market_type="EQUITY",
            start_date=datetime(2025, 7, 1),
            end_date=datetime(2025, 7, 10),
            batch_size=10,
            start_from=None,
            limit=None
        )
        
        # Check that batch progress logging occurred
        batch_progress_calls = [call for call in mock_logger.info.call_args_list 
                               if '📊 EQUITY Progress: Batch' in str(call)]
        
        # Should have batch progress logs
        assert len(batch_progress_calls) >= 1, "Should have at least one batch progress log"
        
        # Check that progress includes batch numbers and percentages
        progress_log_found = False
        for call in batch_progress_calls:
            call_str = str(call)
            if 'Batch' in call_str and '%' in call_str:
                progress_log_found = True
                break
        
        assert progress_log_found, "Batch progress logs should include batch numbers and percentages"


class TestProgressTrackingFormats:
    """Test that progress tracking uses consistent formats."""
    
    def test_progress_message_format_consistency(self):
        """Test that progress messages follow consistent format."""
        # Test different progress message formats
        test_messages = [
            "📊 Processing EQUITY symbols: 10/100 (10.0%)",
            "📊 OPTIONS Prioritization: 50/200 underlyings processed (25.0%) - 1500 symbols selected",
            "📊 EQUITY Progress: Batch 5/10 (50.0%) - Processing symbols 41-50"
        ]
        
        for message in test_messages:
            # Check that message contains emoji
            assert message.startswith("📊"), f"Message should start with 📊: {message}"
            
            # Check that message contains percentage
            assert "%" in message, f"Message should contain percentage: {message}"
            
            # Check that message contains progress ratio (X/Y format)
            import re
            ratio_pattern = r'\d+/\d+'
            assert re.search(ratio_pattern, message), f"Message should contain X/Y ratio: {message}"
    
    def test_percentage_calculation_accuracy(self):
        """Test that percentage calculations are accurate."""
        test_cases = [
            (10, 100, 10.0),
            (25, 200, 12.5),
            (1, 3, 33.3),
            (2, 3, 66.7),
            (100, 100, 100.0),
        ]
        
        for current, total, expected_pct in test_cases:
            calculated_pct = (current / total) * 100
            # Allow small floating point differences
            assert abs(calculated_pct - expected_pct) < 0.1, f"Percentage calculation error: {current}/{total} should be {expected_pct}%, got {calculated_pct}%"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
