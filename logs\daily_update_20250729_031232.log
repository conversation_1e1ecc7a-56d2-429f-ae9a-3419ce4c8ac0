2025-07-29 03:12:32 | INFO | === Daily Data Update Script Started ===
2025-07-29 03:12:32 | INFO | Script Parameters: LogLevel=INFO, DryRun=True, ConfigPath=config.yaml
2025-07-29 03:12:32 | INFO | Testing Python environment...
2025-07-29 03:12:33 | INFO | Python environment activated: Python 3.10.9
2025-07-29 03:12:33 | INFO | Starting NSE symbol processing...
2025-07-29 03:12:33 | INFO | [DRY RUN] Would process NSE symbols
2025-07-29 03:12:33 | INFO | Checking for data gaps across all market types...
2025-07-29 03:12:33 | INFO | [DRY RUN] Would check for data gaps
2025-07-29 03:12:33 | INFO | Processing EQUITY market type...
2025-07-29 03:12:33 | INFO | Starting data update for EQUITY market...
2025-07-29 03:12:33 | INFO | [DRY RUN] Would update EQUITY data for last 2 days
2025-07-29 03:12:33 | INFO | EQUITY processing completed successfully
2025-07-29 03:12:43 | INFO | Processing INDEX market type...
2025-07-29 03:12:43 | INFO | Starting data update for INDEX market...
2025-07-29 03:12:43 | INFO | [DRY RUN] Would update INDEX data for last 2 days
2025-07-29 03:12:43 | INFO | INDEX processing completed successfully
2025-07-29 03:12:53 | INFO | Processing FUTURES market type...
2025-07-29 03:12:53 | INFO | Starting data update for FUTURES market...
2025-07-29 03:12:53 | INFO | [DRY RUN] Would update FUTURES data for last 2 days
2025-07-29 03:12:53 | INFO | FUTURES processing completed successfully
2025-07-29 03:13:03 | INFO | Processing OPTIONS market type...
2025-07-29 03:13:03 | INFO | Starting data update for OPTIONS market...
2025-07-29 03:13:03 | INFO | [DRY RUN] Would update OPTIONS data for last 2 days
2025-07-29 03:13:03 | INFO | OPTIONS processing completed successfully
2025-07-29 03:13:13 | INFO | Generating data health report...
2025-07-29 03:13:13 | INFO | [DRY RUN] Would generate data health report
2025-07-29 03:13:13 | INFO | === Daily Update Summary ===
2025-07-29 03:13:13 | INFO | Total market types: 4
2025-07-29 03:13:13 | INFO | Successful: 4 (EQUITY, INDEX, FUTURES, OPTIONS)
2025-07-29 03:13:13 | INFO | Failed: 0 ()
2025-07-29 03:13:13 | INFO | Overall success: True
2025-07-29 03:13:13 | INFO | [DRY RUN] Would send email: Daily Data Update - SUCCESS
2025-07-29 03:13:13 | INFO | === Daily Data Update Script Completed ===
2025-07-29 03:13:13 | INFO | Final status: SUCCESS
