"""
Command dispatcher that routes commands to appropriate handlers.
"""

from typing import Any

from src.handlers.data_fetch_handler import DataFetchHandler
from src.handlers.maintenance_handler import MaintenanceHandler
from src.handlers.system_handler import SystemHandler
from src.core.logging import get_logger

logger = get_logger(__name__)


class CommandDispatcher:
    """Dispatches commands to appropriate handlers."""
    
    def __init__(self):
        self.data_fetch_handler = DataFetchHandler()
        self.maintenance_handler = MaintenanceHandler()
        self.system_handler = SystemHandler()
    
    def dispatch(self, args: Any) -> bool:
        """Dispatch command to appropriate handler."""
        try:
            # Check for data fetching operations
            if self._is_data_fetch_operation(args):
                return self.data_fetch_handler.execute(args)
            
            # Check for maintenance operations
            elif self._is_maintenance_operation(args):
                return self.maintenance_handler.execute(args)
            
            # Check for system operations
            elif self._is_system_operation(args):
                return self.system_handler.execute(args)
            
            # No operation specified, show help
            else:
                self._show_help()
                return True
                
        except Exception as e:
            logger.error(f"❌ Command dispatch failed: {e}")
            return False
    
    def _is_data_fetch_operation(self, args: Any) -> bool:
        """Check if the operation is data fetching related."""
        return any([
            args.fetch_equity, args.fetch_index, args.fetch_futures, args.fetch_options,
            args.auto_all_symbols, args.fetch_data, args.bulk_all_markets
        ])
    
    def _is_maintenance_operation(self, args: Any) -> bool:
        """Check if the operation is maintenance related."""
        return any([
            args.process_nse_symbols, args.validate_data_integrity,
            args.fix_all_data_issues, args.fix_market_type_tables,
            args.fix_fyers_symbols, args.remove_duplicates,
            args.data_health_report
        ])
    
    def _is_system_operation(self, args: Any) -> bool:
        """Check if the operation is system related."""
        return any([
            args.api, args.init_db, args.view_data
        ])
    
    def _show_help(self) -> None:
        """Show available commands."""
        logger.info("\n💡 Available commands:")
        logger.info("💡 --api                    Start API server")
        logger.info("💡 --init-db               Initialize database")
        logger.info("💡 --process-nse-symbols   Download and process NSE symbols")
        logger.info("💡 --validate-data-integrity  Validate data integrity")
        logger.info("💡 --fetch-data --symbols SYMBOL1 SYMBOL2  Fetch data for specific symbols")
        logger.info("💡 --bulk-all-markets      Fetch data for all market types")
        logger.info("💡 --view-data             View data availability summary")
        logger.info("💡 --fetch-equity SYMBOL   Fetch data for specific equity (e.g., NSE:RELIANCE-EQ)")
        logger.info("💡 --fetch-index SYMBOL    Fetch data for specific index (e.g., NSE:NIFTY50-INDEX)")
        logger.info("💡 --fetch-futures SYMBOL  Fetch data for specific futures (e.g., NSE:RELIANCE25JULFUT)")
        logger.info("💡 --fetch-options SYMBOL  Fetch data for specific options (e.g., NSE:NIFTY25JUL25000CE)")
        logger.info("💡 --auto-all-symbols --market-type TYPE  Fetch data for all symbols of market type")
        logger.info("💡 --fix-fyers-symbols     Fix null fyers_symbol values using exact examples")
        logger.info("💡 --fix-market-type-tables Fix all market type tables")
        logger.info("💡 --fix-all-data-issues   Comprehensive fix for all data issues")
        logger.info("💡 --remove-duplicates     Remove duplicate entries from NSE raw tables")
        logger.info("💡 --data-health-report    Generate comprehensive data health report")
        logger.info("💡 --days N                Specify number of days (default: 10)")
        logger.info("💡 --start-date YYYY-MM-DD Start date for data fetching")
        logger.info("💡 --end-date YYYY-MM-DD   End date for data fetching")
        logger.info("💡 --limit N               Limit number of symbols (for testing)")
        logger.info("💡 --resume-from N         Resume from specific index")
        logger.info("💡 --no-backup             Skip creating backup tables (use with caution)")
        logger.info("💡 --help                  Show all options")
