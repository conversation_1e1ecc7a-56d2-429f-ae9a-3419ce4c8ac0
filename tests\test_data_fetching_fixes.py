"""
Test data fetching functionality across all market types.
"""

import pytest
import sys
import os
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.handlers.data_fetch_handler import DataFetchHandler
from src.database.models import MarketType


class TestDataFetchingFixes:
    """Test suite for data fetching fixes."""
    
    def setup_method(self):
        """Setup test environment."""
        self.handler = DataFetchHandler()
    
    def test_date_parsing_future_dates(self):
        """Test that future dates are properly adjusted."""
        # Mock args with future dates
        args = Mock()
        args.start_date = "2025-07-25"
        args.end_date = "2025-07-26"
        args.days = 1
        
        start_date, end_date = self.handler._parse_date_arguments(args)
        
        # Should adjust to current/past dates
        now = datetime.now()
        assert start_date.date() <= now.date()
        assert end_date.date() <= now.date()
        assert (end_date - start_date).days >= 1
    
    def test_date_parsing_same_day(self):
        """Test that same day dates are handled properly."""
        args = Mock()
        args.start_date = "2025-01-15"
        args.end_date = "2025-01-15"
        args.days = 1
        
        start_date, end_date = self.handler._parse_date_arguments(args)
        
        # Should ensure at least 1 day difference
        assert (end_date - start_date).days >= 1
    
    def test_date_parsing_fallback_to_days(self):
        """Test fallback to days argument when dates not provided."""
        args = Mock()
        args.start_date = None
        args.end_date = None
        args.days = 5
        
        start_date, end_date = self.handler._parse_date_arguments(args)
        
        # Should use days argument
        expected_days = (end_date - start_date).days
        assert expected_days == 5
    
    def test_date_validation_warnings_only(self):
        """Test that date validation only warns, doesn't fail."""
        now = datetime.now()
        future_date = now + timedelta(days=1)
        past_date = now - timedelta(days=1)
        
        # Should return True even for future dates (just warns)
        result = self.handler._validate_date_range(past_date, future_date)
        assert result is True
        
        # Should return True even for small ranges (just warns)
        result = self.handler._validate_date_range(now, now)
        assert result is True
    
    @patch('src.handlers.data_fetch_handler.CLIOperations')
    def test_auto_all_symbols_with_date_fixes(self, mock_cli_ops):
        """Test auto all symbols with date fixes."""
        # Mock successful processing
        mock_cli_ops.return_value.process_symbols_with_resume_and_dates.return_value = {
            'success': True,
            'successful_symbols': 5,
            'failed_symbols': 0
        }
        
        args = Mock()
        args.auto_all_symbols = True
        args.market_type = "INDEX"
        args.start_date = "2025-07-25"  # Future date
        args.end_date = "2025-07-25"    # Future date
        args.days = 1
        args.resume_from = 0
        args.limit = 5
        
        result = self.handler.execute(args)
        
        # Should succeed despite future dates
        assert result is True
        
        # Verify CLI operations was called with adjusted dates
        mock_cli_ops.return_value.process_symbols_with_resume_and_dates.assert_called_once()
        call_args = mock_cli_ops.return_value.process_symbols_with_resume_and_dates.call_args
        
        # Check that dates were adjusted
        start_date = call_args.kwargs['start_date']
        end_date = call_args.kwargs['end_date']
        now = datetime.now()
        
        assert start_date.date() <= now.date()
        assert end_date.date() <= now.date()
    
    @patch('src.handlers.data_fetch_handler.SymbolClassifier')
    @patch('src.handlers.data_fetch_handler.BulkDataService')
    def test_fetch_data_with_symbol_classification(self, mock_bulk_service, mock_classifier):
        """Test fetch data with proper symbol classification."""
        # Mock symbol classification
        mock_classifier.return_value.classify_symbols_batch.return_value = {
            MarketType.EQUITY: [{'symbol': 'RELIANCE-EQ'}],
            MarketType.INDEX: [{'symbol': 'NIFTY50-INDEX'}]
        }
        
        # Mock bulk service
        mock_bulk_service.return_value.populate_all_market_types.return_value = {
            MarketType.EQUITY: {'RELIANCE-EQ': True},
            MarketType.INDEX: {'NIFTY50-INDEX': True}
        }
        
        args = Mock()
        args.fetch_data = True
        args.symbols = ['RELIANCE-EQ', 'NIFTY50-INDEX']
        args.days = 1
        
        result = self.handler.execute(args)
        
        assert result is True
        mock_classifier.return_value.classify_symbols_batch.assert_called_once_with(['RELIANCE-EQ', 'NIFTY50-INDEX'])
        mock_bulk_service.return_value.populate_all_market_types.assert_called_once()
    
    def test_symbol_format_help_display(self):
        """Test that symbol format help is displayed correctly."""
        # This is a simple test to ensure the method exists and runs
        self.handler._show_symbol_format_help()
        # No assertion needed, just ensure it doesn't crash
    
    @patch('src.handlers.data_fetch_handler.BulkDataService')
    def test_process_bulk_results_success(self, mock_bulk_service):
        """Test processing of successful bulk results."""
        results = {
            MarketType.EQUITY: {'RELIANCE-EQ': True, 'TCS-EQ': True},
            MarketType.INDEX: {'NIFTY50-INDEX': True}
        }
        
        result = self.handler._process_bulk_results(results)
        assert result is True
    
    @patch('src.handlers.data_fetch_handler.BulkDataService')
    def test_process_bulk_results_partial_failure(self, mock_bulk_service):
        """Test processing of bulk results with some failures."""
        results = {
            MarketType.EQUITY: {'RELIANCE-EQ': True, 'INVALID-EQ': False},
            MarketType.INDEX: {'NIFTY50-INDEX': True}
        }
        
        result = self.handler._process_bulk_results(results)
        assert result is False  # Should fail if any symbol fails
    
    def test_market_type_validation(self):
        """Test that market types are properly validated."""
        valid_market_types = ['EQUITY', 'INDEX', 'FUTURES', 'OPTIONS']
        
        for market_type in valid_market_types:
            args = Mock()
            args.auto_all_symbols = True
            args.market_type = market_type
            args.start_date = None
            args.end_date = None
            args.days = 1
            args.resume_from = 0
            args.limit = 5
            
            # Should not raise an exception
            try:
                self.handler._handle_auto_all_symbols(args)
            except Exception as e:
                # Expected to fail due to missing dependencies, but not due to market type
                assert "market_type" not in str(e).lower()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
