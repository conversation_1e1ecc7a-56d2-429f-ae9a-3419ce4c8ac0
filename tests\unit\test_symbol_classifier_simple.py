#!/usr/bin/env python3
"""
Simple test for symbol classifier
"""

import sys
from pathlib import Path

# Add src to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.symbol_classifier import SymbolClassifier
from src.core.logging import get_logger

logger = get_logger(__name__)

def test_classifier():
    """Test the symbol classifier."""
    
    logger.info("🧪 Testing Symbol Classifier")
    logger.info("=" * 50)
    
    classifier = SymbolClassifier()
    
    # Test symbols from requirements
    test_symbols = [
        'RELIANCE-EQ',      # Equity
        'NIFTY50-INDEX',    # Index
        'RELIANCE25JULFUT', # Futures
        'NIFTY25JUL25000CE', # Options
        # Space-containing INDEX symbols (newly supported)
        'NIFTY100 EQL WGT-INDEX',
        'HANGSENG BEES-NAV-INDEX',
        'NIFTY50 EQL WGT-INDEX'
    ]
    
    logger.info("Individual symbol classification:")
    for symbol in test_symbols:
        market_type, symbol_info = classifier.classify_symbol(symbol)
        fyers_symbol = symbol_info.get('fyers_symbol')
        
        status = "✅" if market_type else "❌"
        logger.info(f"{status} {symbol:20} -> {market_type.value if market_type else 'UNKNOWN':10} | {fyers_symbol or 'N/A'}")
    
    # Test batch classification
    logger.info("\nBatch classification:")
    classified_batch = classifier.classify_symbols_batch(test_symbols)
    
    for market_type, symbols in classified_batch.items():
        if symbols:
            logger.info(f"{market_type.value}: {len(symbols)} symbols")
            for sym_info in symbols:
                logger.info(f"  - {sym_info['symbol']} -> {sym_info['fyers_symbol']}")
    
    # Test validation
    logger.info("\nValidation test:")
    validation_results = classifier.validate_test_symbols()
    
    all_passed = True
    for symbol, result in validation_results.items():
        status = "✅" if result['overall_success'] else "❌"
        logger.info(f"{status} {symbol}: {result['classified_type'].value if result['classified_type'] else 'UNKNOWN'}")
        if not result['overall_success']:
            all_passed = False
    
    return all_passed

if __name__ == "__main__":
    try:
        success = test_classifier()
        if success:
            logger.info("\n✅ All symbol classifier tests passed!")
        else:
            logger.error("\n❌ Some symbol classifier tests failed!")
        sys.exit(0 if success else 1)
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        sys.exit(1)
