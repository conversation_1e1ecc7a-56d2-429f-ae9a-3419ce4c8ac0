"""
Test the refactored system to ensure all functionality works correctly.
"""

import pytest
import sys
import os
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.handlers.command_dispatcher import CommandDispatcher
from src.handlers.data_fetch_handler import DataFetchHandler
from src.handlers.maintenance_handler import MaintenanceHandler
from src.handlers.system_handler import SystemHandler
from src.database.models import MarketType


class TestRefactoredSystem:
    """Test suite for the refactored system."""
    
    def setup_method(self):
        """Setup test environment."""
        self.dispatcher = CommandDispatcher()
    
    def test_command_dispatcher_initialization(self):
        """Test that command dispatcher initializes all handlers."""
        assert self.dispatcher.data_fetch_handler is not None
        assert self.dispatcher.maintenance_handler is not None
        assert self.dispatcher.system_handler is not None
    
    def test_data_fetch_operation_detection(self):
        """Test that data fetch operations are correctly detected."""
        # Test various data fetch operations
        args = Mock()
        
        # Test fetch equity
        args.fetch_equity = "NSE:RELIANCE-EQ"
        args.fetch_index = None
        args.fetch_futures = None
        args.fetch_options = None
        args.auto_all_symbols = False
        args.fetch_data = False
        args.bulk_all_markets = False
        
        assert self.dispatcher._is_data_fetch_operation(args) is True
        
        # Test auto all symbols
        args.fetch_equity = None
        args.auto_all_symbols = True
        
        assert self.dispatcher._is_data_fetch_operation(args) is True
        
        # Test no data fetch operation
        args.auto_all_symbols = False
        
        assert self.dispatcher._is_data_fetch_operation(args) is False
    
    def test_maintenance_operation_detection(self):
        """Test that maintenance operations are correctly detected."""
        args = Mock()
        
        # Test process NSE symbols
        args.process_nse_symbols = True
        args.validate_data_integrity = False
        args.fix_all_data_issues = False
        args.fix_market_type_tables = False
        args.fix_fyers_symbols = False
        args.remove_duplicates = False
        args.data_health_report = False
        
        assert self.dispatcher._is_maintenance_operation(args) is True
        
        # Test no maintenance operation
        args.process_nse_symbols = False
        
        assert self.dispatcher._is_maintenance_operation(args) is False
    
    def test_system_operation_detection(self):
        """Test that system operations are correctly detected."""
        args = Mock()
        
        # Test API server
        args.api = True
        args.init_db = False
        args.view_data = False
        
        assert self.dispatcher._is_system_operation(args) is True
        
        # Test no system operation
        args.api = False
        
        assert self.dispatcher._is_system_operation(args) is False
    
    @patch('src.handlers.data_fetch_handler.CLIOperations')
    def test_data_fetch_handler_with_valid_symbols(self, mock_cli_ops):
        """Test data fetch handler with valid symbols."""
        # Mock successful processing
        mock_cli_ops.return_value.fetch_specific_symbol_data.return_value = True
        
        handler = DataFetchHandler()
        
        args = Mock()
        args.fetch_equity = "NSE:RELIANCE-EQ"
        args.fetch_index = None
        args.fetch_futures = None
        args.fetch_options = None
        args.auto_all_symbols = False
        args.fetch_data = False
        args.bulk_all_markets = False
        args.days = 5
        
        result = handler.execute(args)
        
        assert result is True
        mock_cli_ops.return_value.fetch_specific_symbol_data.assert_called_once_with(
            "NSE:RELIANCE-EQ", "EQUITY", 5
        )
    
    @patch('src.handlers.maintenance_handler.NSESymbolProcessor')
    def test_maintenance_handler_nse_processing(self, mock_processor):
        """Test maintenance handler NSE symbol processing."""
        # Mock successful processing
        mock_processor.return_value.process_nse_files.return_value = {
            'tables_created': True,
            'download': True,
            'nse_cm_processed': True,
            'nse_fo_processed': True,
            'symbol_mapping_updated': True
        }
        mock_processor.return_value.fix_null_fyers_symbols.return_value = {}
        mock_processor.return_value.get_sample_symbols_by_type.return_value = {}
        mock_processor.return_value.validate_data_integrity.return_value = {
            'validation_passed': True,
            'nse_cm_count': 1000,
            'nse_fo_count': 500,
            'symbol_mapping_count': 1500,
            'equity_count': 800,
            'index_count': 50,
            'futures_count': 300,
            'options_count': 350,
            'missing_data': []
        }
        
        handler = MaintenanceHandler()
        
        args = Mock()
        args.process_nse_symbols = True
        args.validate_data_integrity = False
        args.fix_all_data_issues = False
        args.fix_market_type_tables = False
        args.fix_fyers_symbols = False
        args.remove_duplicates = False
        args.data_health_report = False
        
        result = handler.execute(args)
        
        assert result is True
        mock_processor.return_value.process_nse_files.assert_called_once()
    
    @patch('src.handlers.system_handler.BulkDataService')
    def test_system_handler_view_data(self, mock_bulk_service):
        """Test system handler view data functionality."""
        # Mock data summary
        mock_bulk_service.return_value.get_data_summary.return_value = {
            'total_records': 1000,
            'symbols_count': 10,
            'date_range': {
                'start': '2024-01-01',
                'end': '2024-07-27'
            }
        }
        
        handler = SystemHandler()
        
        args = Mock()
        args.api = False
        args.init_db = False
        args.view_data = True
        
        result = handler.execute(args)
        
        assert result is True
        # Should be called for each market type
        assert mock_bulk_service.return_value.get_data_summary.call_count == 4
    
    def test_date_parsing_edge_cases(self):
        """Test date parsing with various edge cases."""
        handler = DataFetchHandler()
        
        # Test future dates
        args = Mock()
        args.start_date = "2025-12-25"
        args.end_date = "2025-12-26"
        args.days = 1
        
        start_date, end_date = handler._parse_date_arguments(args)
        
        # Should adjust to current/past dates
        now = datetime.now()
        assert start_date.date() <= now.date()
        assert end_date.date() <= now.date()
        assert (end_date - start_date).days >= 1
        
        # Test same day dates
        args.start_date = "2024-01-15"
        args.end_date = "2024-01-15"
        
        start_date, end_date = handler._parse_date_arguments(args)
        
        # Should ensure at least 1 day difference
        assert (end_date - start_date).days >= 1
    
    def test_market_type_validation(self):
        """Test that all market types are supported."""
        valid_market_types = [MarketType.EQUITY, MarketType.INDEX, MarketType.FUTURES, MarketType.OPTIONS]
        
        for market_type in valid_market_types:
            # Should not raise an exception
            assert market_type.value in ['EQUITY', 'INDEX', 'FUTURES', 'OPTIONS']
    
    @patch('src.handlers.command_dispatcher.DataFetchHandler')
    @patch('src.handlers.command_dispatcher.MaintenanceHandler')
    @patch('src.handlers.command_dispatcher.SystemHandler')
    def test_command_dispatcher_routing(self, mock_system, mock_maintenance, mock_data_fetch):
        """Test that command dispatcher routes to correct handlers."""
        # Mock handlers
        mock_data_fetch.return_value.execute.return_value = True
        mock_maintenance.return_value.execute.return_value = True
        mock_system.return_value.execute.return_value = True
        
        dispatcher = CommandDispatcher()
        
        # Test data fetch routing
        args = Mock()
        args.fetch_equity = "NSE:RELIANCE-EQ"
        args.fetch_index = None
        args.fetch_futures = None
        args.fetch_options = None
        args.auto_all_symbols = False
        args.fetch_data = False
        args.bulk_all_markets = False
        args.process_nse_symbols = False
        args.validate_data_integrity = False
        args.fix_all_data_issues = False
        args.fix_market_type_tables = False
        args.fix_fyers_symbols = False
        args.remove_duplicates = False
        args.data_health_report = False
        args.api = False
        args.init_db = False
        args.view_data = False
        
        result = dispatcher.dispatch(args)
        
        assert result is True
        mock_data_fetch.return_value.execute.assert_called_once_with(args)
    
    def test_performance_optimizations_applied(self):
        """Test that performance optimizations are properly applied."""
        from src.core.performance_optimizer import performance_optimizer
        
        # Test that performance optimizer exists
        assert performance_optimizer is not None
        
        # Test cache functionality
        @performance_optimizer.cached_result(ttl_seconds=1)
        def test_cached_function(x):
            return x * 2
        
        # First call should execute
        result1 = test_cached_function(5)
        assert result1 == 10
        
        # Second call should use cache
        result2 = test_cached_function(5)
        assert result2 == 10
        
        # Clear cache
        performance_optimizer.clear_cache()
    
    def test_error_handling_in_handlers(self):
        """Test error handling in all handlers."""
        # Test data fetch handler error handling
        handler = DataFetchHandler()
        
        args = Mock()
        args.fetch_equity = None
        args.fetch_index = None
        args.fetch_futures = None
        args.fetch_options = None
        args.auto_all_symbols = False
        args.fetch_data = False
        args.bulk_all_markets = False
        
        result = handler.execute(args)
        
        # Should return False for invalid operation
        assert result is False
    
    def test_logging_and_timing(self):
        """Test that logging and timing decorators work."""
        from src.core.performance_optimizer import timed
        
        @timed
        def test_timed_function():
            return "test"
        
        # Should not raise an exception
        result = test_timed_function()
        assert result == "test"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
