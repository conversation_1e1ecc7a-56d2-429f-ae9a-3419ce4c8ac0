@echo off
REM Setup automation for Simple Data Service
REM This batch file helps setup the PowerShell automation scripts

echo ========================================
echo Simple Data Service - Automation Setup
echo ========================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [INFO] Running with administrator privileges
) else (
    echo [ERROR] This script requires administrator privileges
    echo [ERROR] Please run as Administrator
    echo.
    pause
    exit /b 1
)

REM Get the script directory
set SCRIPT_DIR=%~dp0
set PROJECT_ROOT=%SCRIPT_DIR%..

echo [INFO] Script directory: %SCRIPT_DIR%
echo [INFO] Project root: %PROJECT_ROOT%
echo.

REM Check if PowerShell scripts exist
if not exist "%SCRIPT_DIR%daily_data_update.ps1" (
    echo [ERROR] daily_data_update.ps1 not found
    pause
    exit /b 1
)

if not exist "%SCRIPT_DIR%setup_task_scheduler.ps1" (
    echo [ERROR] setup_task_scheduler.ps1 not found
    pause
    exit /b 1
)

echo [INFO] PowerShell scripts found
echo.

REM Prompt user for configuration
echo Please configure the automation settings:
echo.

set /p RUN_TIME="Enter daily run time (HH:MM format, default 18:00): "
if "%RUN_TIME%"=="" set RUN_TIME=18:00

set /p USER_ACCOUNT="Enter user account (default %USERNAME%): "
if "%USER_ACCOUNT%"=="" set USER_ACCOUNT=%USERNAME%

echo.
echo Configuration Summary:
echo - Daily run time: %RUN_TIME%
echo - User account: %USER_ACCOUNT%
echo - Project root: %PROJECT_ROOT%
echo.

set /p CONFIRM="Proceed with setup? (Y/N): "
if /i not "%CONFIRM%"=="Y" (
    echo Setup cancelled
    pause
    exit /b 0
)

echo.
echo [INFO] Setting up Windows Task Scheduler...

REM Run the PowerShell setup script
powershell.exe -ExecutionPolicy Bypass -File "%SCRIPT_DIR%setup_task_scheduler.ps1" -RunTime "%RUN_TIME%" -UserAccount "%USER_ACCOUNT%" -Force

if %errorLevel% == 0 (
    echo.
    echo [SUCCESS] Automation setup completed successfully!
    echo.
    echo The following tasks have been created:
    echo - SimpleDataService-DailyUpdate: Runs daily at %RUN_TIME%
    echo - SimpleDataService-DailyUpdate-WeeklyMaintenance: Runs weekly on Sunday at 02:00
    echo.
    echo You can monitor these tasks using Windows Task Scheduler
    echo Logs will be saved in the logs/ directory
    echo.
) else (
    echo.
    echo [ERROR] Automation setup failed
    echo Please check the error messages above
    echo.
)

echo.
echo ========================================
echo Setup Complete
echo ========================================
pause
