"""
Gap Filling Service

This service automatically checks database tables for missing data and generates
prioritized symbol lists for gap-filling. It identifies symbols with incomplete
data and prioritizes them by earliest missing date.
"""

import logging
from datetime import datetime, timedelta, date
from typing import Dict, List, Optional, Tuple, Set
from dataclasses import dataclass
from sqlalchemy.orm import Session
from sqlalchemy import text, func

from src.database.connection import get_db
from src.database.models import MarketType, EquityOHLCV, IndexOHLCV, FuturesOHLCV, OptionsOHLCV

logger = logging.getLogger(__name__)


@dataclass
class GapInfo:
    """Information about a data gap for a symbol."""
    symbol: str
    fyers_symbol: str
    market_type: MarketType
    latest_date: Optional[date]
    missing_days: int
    priority_score: float  # Higher score = higher priority


class GapFillingService:
    """Service for identifying and prioritizing data gaps."""
    
    def __init__(self, db_session: Optional[Session] = None):
        """Initialize the gap filling service."""
        self.db = db_session or next(get_db())
        
        # Map market types to their OHLCV models and tables
        self.model_mapping = {
            MarketType.EQUITY: EquityOHLCV,
            MarketType.INDEX: IndexOHLCV,
            MarketType.FUTURES: FuturesOHLCV,
            MarketType.OPTIONS: OptionsOHLCV
        }
        
        self.table_mapping = {
            MarketType.EQUITY: 'equity_ohlcv',
            MarketType.INDEX: 'index_ohlcv',
            MarketType.FUTURES: 'futures_ohlcv',
            MarketType.OPTIONS: 'options_ohlcv'
        }
    
    def analyze_gaps_for_market_type(
        self, 
        market_type: MarketType, 
        max_gap_days: int = 30,
        limit: Optional[int] = None
    ) -> List[GapInfo]:
        """
        Analyze data gaps for a specific market type.
        
        Args:
            market_type: Market type to analyze
            max_gap_days: Maximum number of days to consider as a gap
            limit: Optional limit on number of symbols to analyze
            
        Returns:
            List of GapInfo objects sorted by priority (highest first)
        """
        try:
            logger.info(f"🔍 Analyzing data gaps for {market_type.value}...")
            
            table_name = self.table_mapping.get(market_type)
            if not table_name:
                logger.error(f"No table mapping found for {market_type.value}")
                return []
            
            # Get all symbols and their latest data dates
            symbol_dates = self._get_symbol_latest_dates(table_name, limit)
            
            if not symbol_dates:
                logger.warning(f"No symbols found in {table_name}")
                return []
            
            # Calculate gaps and priorities
            gaps = []
            today = date.today()
            
            for fyers_symbol, latest_date in symbol_dates.items():
                # Extract base symbol from Fyers symbol
                base_symbol = self._extract_base_symbol(fyers_symbol)
                
                # Calculate missing days
                if latest_date:
                    missing_days = (today - latest_date).days
                else:
                    # No data at all - highest priority
                    missing_days = max_gap_days + 1
                    latest_date = today - timedelta(days=missing_days)
                
                # Only consider symbols with significant gaps
                if missing_days > 1:  # More than 1 day missing
                    priority_score = self._calculate_priority_score(
                        missing_days, max_gap_days, market_type
                    )
                    
                    gap_info = GapInfo(
                        symbol=base_symbol,
                        fyers_symbol=fyers_symbol,
                        market_type=market_type,
                        latest_date=latest_date,
                        missing_days=missing_days,
                        priority_score=priority_score
                    )
                    
                    gaps.append(gap_info)
            
            # Sort by priority score (highest first)
            gaps.sort(key=lambda x: x.priority_score, reverse=True)
            
            logger.info(f"📊 Found {len(gaps)} symbols with data gaps in {market_type.value}")
            if gaps:
                logger.info(f"   Top priority: {gaps[0].symbol} ({gaps[0].missing_days} days missing)")
                logger.info(f"   Oldest gap: {max(gaps, key=lambda x: x.missing_days).missing_days} days")
            
            return gaps
            
        except Exception as e:
            logger.error(f"Error analyzing gaps for {market_type.value}: {e}")
            return []
    
    def _get_symbol_latest_dates(self, table_name: str, limit: Optional[int] = None) -> Dict[str, Optional[date]]:
        """Get the latest data date for each symbol in a table."""
        try:
            # Build query to get latest date for each symbol
            # Note: OHLCV tables use 'datetime' column, not 'timestamp'
            query = f"""
                SELECT
                    fyers_symbol,
                    MAX(DATE(datetime)) as latest_date
                FROM public.{table_name}
                WHERE fyers_symbol IS NOT NULL
                GROUP BY fyers_symbol
            """
            
            if limit:
                query += f" LIMIT {limit}"
            
            result = self.db.execute(text(query))
            
            symbol_dates = {}
            for row in result:
                fyers_symbol = row[0]
                latest_date = row[1]  # This is already a date object
                symbol_dates[fyers_symbol] = latest_date
            
            return symbol_dates
            
        except Exception as e:
            logger.error(f"Error getting symbol latest dates from {table_name}: {e}")
            return {}
    
    def _extract_base_symbol(self, fyers_symbol: str) -> str:
        """Extract base symbol from Fyers symbol format."""
        try:
            # Remove NSE: prefix if present
            if fyers_symbol.startswith('NSE:'):
                symbol = fyers_symbol[4:]
            else:
                symbol = fyers_symbol
            
            # For options/futures, extract the underlying symbol
            # Example: BANKNIFTY25JUL56200CE -> BANKNIFTY
            if any(month in symbol for month in ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN',
                                                 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC']):
                # Find the first occurrence of a month abbreviation
                for month in ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN',
                             'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC']:
                    if month in symbol:
                        base_symbol = symbol.split(month)[0]
                        return base_symbol
            
            # For equity symbols, remove -EQ suffix
            if symbol.endswith('-EQ'):
                return symbol[:-3]
            
            return symbol
            
        except Exception as e:
            logger.debug(f"Error extracting base symbol from {fyers_symbol}: {e}")
            return fyers_symbol
    
    def _calculate_priority_score(self, missing_days: int, max_gap_days: int, market_type: MarketType) -> float:
        """Calculate priority score for a symbol based on missing days and market type."""
        try:
            # Base score based on missing days (more missing = higher priority)
            base_score = min(missing_days / max_gap_days, 1.0) * 100
            
            # Market type multipliers (some markets are more important)
            market_multipliers = {
                MarketType.INDEX: 1.2,      # Indices are important
                MarketType.EQUITY: 1.0,     # Standard priority
                MarketType.FUTURES: 1.1,    # Slightly higher priority
                MarketType.OPTIONS: 0.9     # Slightly lower priority (more volatile)
            }
            
            multiplier = market_multipliers.get(market_type, 1.0)
            
            # Boost score for symbols with no data at all
            if missing_days > max_gap_days:
                base_score = 150  # Very high priority
            
            return base_score * multiplier
            
        except Exception as e:
            logger.debug(f"Error calculating priority score: {e}")
            return 50.0  # Default medium priority
    
    def get_prioritized_symbols_for_gap_filling(
        self, 
        market_types: Optional[List[MarketType]] = None,
        max_symbols_per_type: int = 50,
        max_gap_days: int = 30
    ) -> Dict[MarketType, List[str]]:
        """
        Get prioritized symbol lists for gap filling across market types.
        
        Args:
            market_types: List of market types to analyze (None = all)
            max_symbols_per_type: Maximum symbols to return per market type
            max_gap_days: Maximum gap days to consider
            
        Returns:
            Dictionary mapping market types to prioritized symbol lists
        """
        try:
            if market_types is None:
                market_types = list(MarketType)
            
            prioritized_symbols = {}
            
            for market_type in market_types:
                logger.info(f"🎯 Getting prioritized symbols for {market_type.value}...")
                
                # Analyze gaps for this market type
                gaps = self.analyze_gaps_for_market_type(
                    market_type=market_type,
                    max_gap_days=max_gap_days,
                    limit=max_symbols_per_type * 2  # Get more to filter from
                )
                
                # Extract symbol list (limit to max_symbols_per_type)
                symbols = [gap.fyers_symbol for gap in gaps[:max_symbols_per_type]]
                
                if symbols:
                    prioritized_symbols[market_type] = symbols
                    logger.info(f"   📋 {len(symbols)} symbols prioritized for gap filling")
                else:
                    logger.info(f"   ✅ No significant gaps found in {market_type.value}")
            
            return prioritized_symbols
            
        except Exception as e:
            logger.error(f"Error getting prioritized symbols for gap filling: {e}")
            return {}
    
    def generate_gap_filling_report(
        self, 
        market_types: Optional[List[MarketType]] = None,
        max_gap_days: int = 30
    ) -> str:
        """
        Generate a detailed gap filling report.
        
        Args:
            market_types: List of market types to analyze
            max_gap_days: Maximum gap days to consider
            
        Returns:
            Path to the generated report file
        """
        try:
            if market_types is None:
                market_types = list(MarketType)
            
            # Create report file
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            report_path = f"logs/gap_filling_report_{timestamp}.txt"
            
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(f"Gap Filling Analysis Report\n")
                f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"Max Gap Days: {max_gap_days}\n")
                f.write("=" * 80 + "\n\n")
                
                total_gaps = 0
                
                for market_type in market_types:
                    f.write(f"\n{market_type.value.upper()} ANALYSIS\n")
                    f.write("-" * 40 + "\n")
                    
                    gaps = self.analyze_gaps_for_market_type(market_type, max_gap_days)
                    total_gaps += len(gaps)
                    
                    if gaps:
                        f.write(f"Symbols with gaps: {len(gaps)}\n")
                        f.write(f"Avg missing days: {sum(g.missing_days for g in gaps) / len(gaps):.1f}\n")
                        f.write(f"Max missing days: {max(g.missing_days for g in gaps)}\n\n")
                        
                        f.write("Top 10 Priority Symbols:\n")
                        for i, gap in enumerate(gaps[:10], 1):
                            f.write(f"{i:2d}. {gap.fyers_symbol:<30} | {gap.missing_days:3d} days | Score: {gap.priority_score:.1f}\n")
                    else:
                        f.write("No significant gaps found.\n")
                    
                    f.write("\n")
                
                f.write(f"\nSUMMARY\n")
                f.write("-" * 20 + "\n")
                f.write(f"Total symbols with gaps: {total_gaps}\n")
                f.write(f"Market types analyzed: {len(market_types)}\n")
            
            logger.info(f"📄 Gap filling report generated: {report_path}")
            return report_path
            
        except Exception as e:
            logger.error(f"Error generating gap filling report: {e}")
            return ""
