"""
Handler for maintenance operations.
"""

from typing import Any

from src.handlers.base_handler import <PERSON>CommandHandler
from src.core.nse_symbol_processor import NSESymbolProcessor
from src.core.data_integrity_validator import DataIntegrityValidator
from src.services.data_management_service import DataManagementService
from src.services.duplicate_removal_service import DuplicateRemovalService
from src.helpers.cli_operations import CLIOperations
from src.core.logging import get_logger

logger = get_logger(__name__)


class MaintenanceHandler(BaseCommandHandler):
    """Handler for maintenance and data management operations."""
    
    def __init__(self):
        super().__init__()
        self.processor = NSESymbolProcessor()
        self.validator = DataIntegrityValidator()
        self.data_mgmt_service = DataManagementService()
        self.duplicate_service = DuplicateRemovalService()
        self.cli_ops = CLIOperations()
    
    def execute(self, args: Any) -> bool:
        """Execute maintenance operation based on arguments."""
        try:
            if args.process_nse_symbols:
                return self._handle_process_nse_symbols()
            elif args.validate_data_integrity:
                return self._handle_validate_data_integrity()
            elif args.fix_all_data_issues:
                return self._handle_fix_all_data_issues(args)
            elif args.fix_market_type_tables:
                return self._handle_fix_market_type_tables(args)
            elif args.fix_fyers_symbols:
                return self._handle_fix_fyers_symbols()
            elif args.remove_duplicates:
                return self._handle_remove_duplicates(args)
            elif args.data_health_report:
                return self._handle_data_health_report()
            else:
                self.logger.error("❌ No valid maintenance operation specified")
                return False
                
        except Exception as e:
            return self.handle_error(e, "Maintenance operation")
    
    def _handle_process_nse_symbols(self) -> bool:
        """Handle NSE symbol processing."""
        try:
            self.logger.info("📥 Processing NSE symbols with daily backup and filtering...")
            
            results = self.processor.process_nse_files()
            
            self.logger.info("📊 NSE Symbol Processing Results:")
            self.logger.info(f"  Tables Created: {'✅' if results['tables_created'] else '❌'}")
            self.logger.info(f"  Download: {'✅' if results['download'] else '❌'}")
            self.logger.info(f"  NSE_CM Processed: {'✅' if results['nse_cm_processed'] else '❌'}")
            self.logger.info(f"  NSE_FO Processed: {'✅' if results['nse_fo_processed'] else '❌'}")
            self.logger.info(f"  Symbol Mapping Updated: {'✅' if results['symbol_mapping_updated'] else '❌'}")
            
            # Fix null fyers_symbol values
            self.logger.info("🔧 Fixing null fyers_symbol values in market type tables...")
            fix_results = self.processor.fix_null_fyers_symbols()
            if fix_results:
                self.logger.info("📊 Fixed null fyers_symbol values:")
                for table, count in fix_results.items():
                    if count > 0:
                        self.logger.info(f"  {table}: {count} rows updated")
                    else:
                        self.logger.info(f"  {table}: No null values found")
            
            # Get sample symbols for testing
            sample_symbols = self.processor.get_sample_symbols_by_type()
            if sample_symbols:
                self.logger.info("\n🎯 Sample symbols for testing (one from each market type):")
                for market_type, symbol in sample_symbols.items():
                    self.logger.info(f"  {market_type}: {symbol}")
            
            # Validate data integrity
            integrity_report = self.processor.validate_data_integrity()
            self._log_integrity_report(integrity_report)
            
            success = all(results.values()) and integrity_report['validation_passed']
            if success:
                return self.log_success("NSE symbol processing completed successfully")
            else:
                self.logger.error("❌ NSE symbol processing had issues")
                return False
                
        except Exception as e:
            return self.handle_error(e, "NSE symbol processing")
    
    def _handle_validate_data_integrity(self) -> bool:
        """Handle data integrity validation."""
        try:
            self.logger.info("🔍 Validating data integrity across NSE tables...")
            
            validation_results = self.validator.validate_all()
            
            # Display summary
            summary = validation_results.get('summary', {})
            overall_status = summary.get('overall_status', 'UNKNOWN')
            total_issues = summary.get('total_issues', 0)
            
            self.logger.info(f"Data Integrity Validation: {'✅ HEALTHY' if overall_status == 'HEALTHY' else '⚠️ NEEDS ATTENTION'}")
            self.logger.info(f"Total Issues Found: {total_issues}")
            
            self._log_validation_details(validation_results)
            
            # Display issues and recommendations
            if summary.get('issues'):
                self.logger.warning("\n🔍 Issues Found:")
                for issue in summary['issues']:
                    self.logger.warning(f"   ❌ {issue}")
            
            if summary.get('recommendations'):
                self.logger.info("\n💡 Recommendations:")
                for rec in summary['recommendations']:
                    self.logger.info(f"   🔧 {rec}")
            
            success = overall_status == 'HEALTHY'
            if success:
                return self.log_success("Data integrity validation passed")
            else:
                self.logger.warning("⚠️ Data integrity validation found issues")
                return False
                
        except Exception as e:
            return self.handle_error(e, "Data integrity validation")
    
    def _handle_fix_all_data_issues(self, args: Any) -> bool:
        """Handle comprehensive data management."""
        try:
            self.logger.info("🔧 Starting comprehensive data management process...")
            
            create_backup = not args.no_backup
            results = self.data_mgmt_service.fix_all_data_issues(create_backup=create_backup)
            
            # Display results
            summary = results.get('summary', {})
            self.logger.info(f"\n📊 Data Management Results:")
            self.logger.info(f"   Overall Status: {summary.get('overall_status', 'UNKNOWN')}")
            self.logger.info(f"   Data Quality Score: {summary.get('data_quality_score', 0):.1f}%")
            self.logger.info(f"   Issues Fixed: {summary.get('total_issues_fixed', 0):,}")
            self.logger.info(f"   Remaining Issues: {summary.get('remaining_issues', 0)}")
            
            self._log_issues_and_recommendations(results)
            
            success = summary.get('overall_status') in ['SUCCESS', 'EXCELLENT', 'GOOD']
            if success:
                return self.log_success("Data management completed successfully")
            else:
                self.logger.error("❌ Data management had issues")
                return False
                
        except Exception as e:
            return self.handle_error(e, "Data management")
    
    def _handle_fix_market_type_tables(self, args: Any) -> bool:
        """Handle market type table fixes."""
        try:
            self.logger.info("🔧 Fixing all market type tables...")
            
            create_backup = not args.no_backup
            results = self.data_mgmt_service.fix_all_market_type_tables(create_backup=create_backup)
            
            # Display results
            summary = results.get('summary', {})
            self.logger.info(f"\n📊 Market Type Table Fix Results:")
            self.logger.info(f"   Overall Status: {summary.get('overall_status', 'UNKNOWN')}")
            self.logger.info(f"   Tables Processed: {summary.get('tables_processed', 0)}")
            self.logger.info(f"   Fyers Symbols Updated: {summary.get('fyers_symbols_updated', 0)}")
            
            self._log_issues_and_recommendations(results)
            
            success = summary.get('overall_status') in ['SUCCESS', 'EXCELLENT']
            if success:
                return self.log_success("Market type table fix completed successfully")
            else:
                self.logger.error("❌ Market type table fix had issues")
                return False
                
        except Exception as e:
            return self.handle_error(e, "Market type table fix")
    
    def _handle_fix_fyers_symbols(self) -> bool:
        """Handle fyers symbol fixes."""
        try:
            self.logger.info("🔧 Fixing fyers_symbol columns with exact examples...")
            
            success = self.cli_ops.fix_fyers_symbols_with_examples()
            
            if success:
                return self.log_success("Successfully fixed fyers_symbol columns")
            else:
                self.logger.error("❌ Failed to fix fyers_symbol columns")
                return False
                
        except Exception as e:
            return self.handle_error(e, "Fyers symbol fix")
    
    def _handle_remove_duplicates(self, args: Any) -> bool:
        """Handle duplicate removal."""
        try:
            self.logger.info("🧹 Removing duplicate entries from NSE raw tables...")
            
            # Create backup if not skipped
            if not args.no_backup:
                self.logger.info("📦 Creating backup tables...")
                backup_info = self.duplicate_service.create_backup_before_removal()
                self.logger.info("✅ Backup tables created")
            
            # Get initial statistics
            initial_stats = self.duplicate_service.get_duplicate_statistics()
            self.logger.info("📊 Initial duplicate statistics:")
            for table, stats in initial_stats.items():
                self.logger.info(f"   {table}: {stats['duplicate_rows']:,} duplicates ({stats['duplicate_percentage']:.1f}%)")
            
            # Remove duplicates
            results = self.duplicate_service.remove_all_duplicates()
            
            # Verify removal
            integrity_check = self.duplicate_service.verify_data_integrity_after_removal()
            
            self.logger.info("📊 Duplicate removal results:")
            for table, stats in results.items():
                removed = stats.get('duplicates_removed', 0)
                verified = integrity_check.get(table, False)
                status = "✅" if verified else "⚠️"
                self.logger.info(f"   {table}: {removed:,} duplicates removed {status}")
            
            success = all(integrity_check.values())
            if success:
                return self.log_success("Duplicate removal completed successfully")
            else:
                self.logger.error("❌ Duplicate removal had issues")
                return False
                
        except Exception as e:
            return self.handle_error(e, "Duplicate removal")
    
    def _handle_data_health_report(self) -> bool:
        """Handle data health report generation."""
        try:
            self.logger.info("📋 Generating comprehensive data health report...")
            
            health_report = self.data_mgmt_service.get_data_health_report()
            
            overall_health = health_report.get('overall_health', {})
            self.logger.info(f"\n📊 Data Health Report:")
            self.logger.info(f"   Overall Health: {overall_health.get('status', 'UNKNOWN')} ({overall_health.get('score', 0):.1f}%)")
            self.logger.info(f"   Issues Count: {overall_health.get('issues_count', 0)}")
            
            if overall_health.get('issues'):
                self.logger.warning(f"\n⚠️  Health Issues:")
                for issue in overall_health['issues']:
                    self.logger.warning(f"   - {issue}")
            
            # Display duplicate statistics
            duplicate_stats = health_report.get('duplicate_statistics', {})
            if duplicate_stats:
                self.logger.info(f"\n📊 Duplicate Statistics:")
                for table, stats in duplicate_stats.items():
                    self.logger.info(f"   {table}: {stats.get('total_rows', 0):,} total, {stats.get('duplicate_rows', 0):,} duplicates")
            
            success = overall_health.get('score', 0) >= 80
            if success:
                return self.log_success("Data health report generated successfully")
            else:
                self.logger.warning("⚠️ Data health report shows issues")
                return False
                
        except Exception as e:
            return self.handle_error(e, "Data health report generation")
    
    def _log_integrity_report(self, integrity_report: dict) -> None:
        """Log integrity report details."""
        self.logger.info(f"\n🔍 Enhanced Data Integrity Validation: {'✅ PASSED' if integrity_report['validation_passed'] else '❌ FAILED'}")
        self.logger.info(f"  NSE_CM Records: {integrity_report['nse_cm_count']:,}")
        self.logger.info(f"  NSE_FO Records: {integrity_report['nse_fo_count']:,}")
        self.logger.info(f"  Symbol Mapping Records: {integrity_report['symbol_mapping_count']:,}")
        
        # Show pattern-based filtering results
        if 'filtered_symbols_count' in integrity_report:
            self.logger.info(f"  Filtered Symbols (Pattern-based): {integrity_report['filtered_symbols_count']:,}")
            self.logger.info(f"  Coverage: {integrity_report['coverage_percentage']:.1f}%")
        
        # Show market type distribution
        self.logger.info(f"  EQUITY Symbols: {integrity_report['equity_count']:,}")
        self.logger.info(f"  INDEX Symbols: {integrity_report['index_count']:,}")
        self.logger.info(f"  FUTURES Symbols: {integrity_report['futures_count']:,}")
        self.logger.info(f"  OPTIONS Symbols: {integrity_report['options_count']:,}")
        
        # Show pattern validation results
        if 'pattern_validation' in integrity_report:
            self.logger.info("\n📊 Pattern Validation Results:")
            for pattern, validation in integrity_report['pattern_validation'].items():
                status = "✅" if validation['has_symbols'] else "❌"
                self.logger.info(f"  {pattern}: {status} {validation['count']:,} symbols")
        
        if integrity_report['missing_data']:
            self.logger.warning("\n⚠️  Data integrity issues:")
            for issue in integrity_report['missing_data']:
                self.logger.warning(f"    - {issue}")
    
    def _log_validation_details(self, validation_results: dict) -> None:
        """Log validation details."""
        # Display raw data integrity
        raw_data = validation_results.get('raw_data_integrity', {})
        if 'nse_cm_raw' in raw_data:
            cm_data = raw_data['nse_cm_raw']
            self.logger.info(f"NSE_CM Records: {cm_data.get('total_rows', 0):,} (Unique: {cm_data.get('unique_symbols', 0):,})")
        
        if 'nse_fo_raw' in raw_data:
            fo_data = raw_data['nse_fo_raw']
            self.logger.info(f"NSE_FO Records: {fo_data.get('total_rows', 0):,} (Unique: {fo_data.get('unique_symbols', 0):,})")
        
        # Display symbol mapping integrity
        mapping_data = validation_results.get('symbol_mapping_integrity', {})
        if 'total_symbols' in mapping_data:
            self.logger.info(f"Symbol Mapping Records: {mapping_data['total_symbols']:,}")
            market_dist = mapping_data.get('market_type_distribution', {})
            for market_type, count in market_dist.items():
                self.logger.info(f"  {market_type}: {count:,}")
        
        # Display OHLCV table status
        ohlcv_data = validation_results.get('ohlcv_table_integrity', {})
        for table, info in ohlcv_data.items():
            if isinstance(info, dict) and 'exists' in info:
                status = "✅" if info.get('status') == 'GOOD' else "⚠️"
                hypertable = "✅" if info.get('is_hypertable') else "❌"
                fyers_col = "✅" if info.get('fyers_column_exists') else "❌"
                self.logger.info(f"{table}: {status} (Hypertable: {hypertable}, Fyers Column: {fyers_col})")
    
    def _log_issues_and_recommendations(self, results: dict) -> None:
        """Log issues and recommendations."""
        if results.get('issues_found'):
            self.logger.warning(f"\n⚠️  Issues Found:")
            for issue in results['issues_found']:
                self.logger.warning(f"   - {issue}")
        
        if results.get('recommendations'):
            self.logger.info(f"\n💡 Recommendations:")
            for rec in results['recommendations']:
                self.logger.info(f"   - {rec}")
