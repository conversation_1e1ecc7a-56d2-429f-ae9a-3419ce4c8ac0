# Setup Windows Task Scheduler for Daily Data Updates
# This script creates scheduled tasks for automated daily data updates

param(
    [string]$TaskName = "SimpleDataService-DailyUpdate",
    [string]$TaskDescription = "Daily automated data update for Simple Data Service",
    [string]$RunTime = "18:00",  # 6:00 PM
    [string]$UserAccount = $env:USERNAME,
    [switch]$Force = $false
)

# Script configuration
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectRoot = Split-Path -Parent $ScriptDir
$DailyUpdateScript = Join-Path $ScriptDir "daily_data_update.ps1"

function Write-Log {
    param(
        [string]$Message,
        [string]$Level = "INFO"
    )
    $Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "[$Timestamp] $Level: $Message"
}

function Test-AdminRights {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

function Remove-ExistingTask {
    param([string]$Name)
    
    try {
        $existingTask = Get-ScheduledTask -TaskName $Name -ErrorAction SilentlyContinue
        if ($existingTask) {
            Write-Log "Removing existing task: $Name" "INFO"
            Unregister-ScheduledTask -TaskName $Name -Confirm:$false
            Write-Log "Existing task removed successfully" "INFO"
        }
    }
    catch {
        Write-Log "Error removing existing task: $($_.Exception.Message)" "ERROR"
    }
}

function Create-DailyUpdateTask {
    Write-Log "Creating daily update scheduled task..." "INFO"
    
    try {
        # Task action - run the PowerShell script
        $action = New-ScheduledTaskAction -Execute "PowerShell.exe" `
                                         -Argument "-ExecutionPolicy Bypass -File `"$DailyUpdateScript`""
        
        # Task trigger - daily at specified time
        $trigger = New-ScheduledTaskTrigger -Daily -At $RunTime
        
        # Task settings
        $settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries `
                                                 -StartWhenAvailable -RunOnlyIfNetworkAvailable `
                                                 -DontStopOnIdleEnd -RestartCount 3 `
                                                 -RestartInterval (New-TimeSpan -Minutes 5)
        
        # Task principal (user context)
        $principal = New-ScheduledTaskPrincipal -UserId $UserAccount -LogonType Interactive -RunLevel Highest
        
        # Register the task
        Register-ScheduledTask -TaskName $TaskName -Action $action -Trigger $trigger `
                              -Settings $settings -Principal $principal -Description $TaskDescription
        
        Write-Log "Daily update task created successfully" "INFO"
        return $true
    }
    catch {
        Write-Log "Failed to create daily update task: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Create-WeeklyMaintenanceTask {
    $maintenanceTaskName = "$TaskName-WeeklyMaintenance"
    
    Write-Log "Creating weekly maintenance scheduled task..." "INFO"
    
    try {
        # Create maintenance script arguments
        $maintenanceArgs = "-ExecutionPolicy Bypass -Command `"& '$DailyUpdateScript' -DryRun:`$false; python '$ProjectRoot\main.py' --fix-all-data-issues --no-backup`""
        
        # Task action
        $action = New-ScheduledTaskAction -Execute "PowerShell.exe" -Argument $maintenanceArgs
        
        # Task trigger - weekly on Sunday at 2:00 AM
        $trigger = New-ScheduledTaskTrigger -Weekly -DaysOfWeek Sunday -At "02:00"
        
        # Task settings
        $settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries `
                                                 -StartWhenAvailable -RunOnlyIfNetworkAvailable `
                                                 -DontStopOnIdleEnd -RestartCount 2 `
                                                 -RestartInterval (New-TimeSpan -Minutes 10) `
                                                 -ExecutionTimeLimit (New-TimeSpan -Hours 2)
        
        # Task principal
        $principal = New-ScheduledTaskPrincipal -UserId $UserAccount -LogonType Interactive -RunLevel Highest
        
        # Register the task
        Register-ScheduledTask -TaskName $maintenanceTaskName -Action $action -Trigger $trigger `
                              -Settings $settings -Principal $principal `
                              -Description "Weekly maintenance for Simple Data Service (data integrity, cleanup)"
        
        Write-Log "Weekly maintenance task created successfully" "INFO"
        return $true
    }
    catch {
        Write-Log "Failed to create weekly maintenance task: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Test-TaskSchedulerSetup {
    Write-Log "Testing task scheduler setup..." "INFO"
    
    try {
        # Check if daily task exists and is enabled
        $dailyTask = Get-ScheduledTask -TaskName $TaskName -ErrorAction SilentlyContinue
        if ($dailyTask -and $dailyTask.State -eq "Ready") {
            Write-Log "Daily task is properly configured and ready" "INFO"
        } else {
            Write-Log "Daily task is not properly configured" "WARNING"
            return $false
        }
        
        # Check if weekly task exists
        $weeklyTaskName = "$TaskName-WeeklyMaintenance"
        $weeklyTask = Get-ScheduledTask -TaskName $weeklyTaskName -ErrorAction SilentlyContinue
        if ($weeklyTask -and $weeklyTask.State -eq "Ready") {
            Write-Log "Weekly maintenance task is properly configured and ready" "INFO"
        } else {
            Write-Log "Weekly maintenance task is not properly configured" "WARNING"
        }
        
        # Test script accessibility
        if (-not (Test-Path $DailyUpdateScript)) {
            Write-Log "Daily update script not found: $DailyUpdateScript" "ERROR"
            return $false
        }
        
        Write-Log "Task scheduler setup test completed successfully" "INFO"
        return $true
    }
    catch {
        Write-Log "Task scheduler setup test failed: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Show-TaskInfo {
    Write-Log "=== Task Scheduler Configuration ===" "INFO"
    Write-Log "Daily Task Name: $TaskName" "INFO"
    Write-Log "Run Time: $RunTime daily" "INFO"
    Write-Log "User Account: $UserAccount" "INFO"
    Write-Log "Script Path: $DailyUpdateScript" "INFO"
    Write-Log "Project Root: $ProjectRoot" "INFO"
    
    try {
        $task = Get-ScheduledTask -TaskName $TaskName -ErrorAction SilentlyContinue
        if ($task) {
            Write-Log "Task Status: $($task.State)" "INFO"
            $taskInfo = Get-ScheduledTaskInfo -TaskName $TaskName
            Write-Log "Last Run Time: $($taskInfo.LastRunTime)" "INFO"
            Write-Log "Next Run Time: $($taskInfo.NextRunTime)" "INFO"
            Write-Log "Last Task Result: $($taskInfo.LastTaskResult)" "INFO"
        }
    }
    catch {
        Write-Log "Could not retrieve task information: $($_.Exception.Message)" "WARNING"
    }
}

# Main execution
Write-Log "=== Task Scheduler Setup Started ===" "INFO"

# Check if running as administrator
if (-not (Test-AdminRights)) {
    Write-Log "This script requires administrator privileges to create scheduled tasks" "ERROR"
    Write-Log "Please run PowerShell as Administrator and try again" "ERROR"
    exit 1
}

# Validate script path
if (-not (Test-Path $DailyUpdateScript)) {
    Write-Log "Daily update script not found: $DailyUpdateScript" "ERROR"
    Write-Log "Please ensure the daily_data_update.ps1 script exists in the scripts directory" "ERROR"
    exit 1
}

try {
    # Remove existing tasks if Force is specified
    if ($Force) {
        Write-Log "Force flag specified, removing existing tasks..." "INFO"
        Remove-ExistingTask -Name $TaskName
        Remove-ExistingTask -Name "$TaskName-WeeklyMaintenance"
    }
    
    # Check if task already exists
    $existingTask = Get-ScheduledTask -TaskName $TaskName -ErrorAction SilentlyContinue
    if ($existingTask -and -not $Force) {
        Write-Log "Task '$TaskName' already exists. Use -Force to recreate it." "WARNING"
        Show-TaskInfo
        exit 0
    }
    
    # Create the scheduled tasks
    $dailySuccess = Create-DailyUpdateTask
    $weeklySuccess = Create-WeeklyMaintenanceTask
    
    if ($dailySuccess) {
        Write-Log "Daily update task setup completed successfully" "INFO"
        
        # Test the setup
        if (Test-TaskSchedulerSetup) {
            Write-Log "Task scheduler setup validation passed" "INFO"
        } else {
            Write-Log "Task scheduler setup validation failed" "WARNING"
        }
        
        # Show task information
        Show-TaskInfo
        
        Write-Log "=== Setup Complete ===" "INFO"
        Write-Log "The daily data update will run automatically at $RunTime every day" "INFO"
        Write-Log "Weekly maintenance will run every Sunday at 2:00 AM" "INFO"
        Write-Log "You can monitor the tasks using Task Scheduler or check the logs in the logs/ directory" "INFO"
        
    } else {
        Write-Log "Failed to setup daily update task" "ERROR"
        exit 1
    }
    
} catch {
    Write-Log "Critical error during task scheduler setup: $($_.Exception.Message)" "ERROR"
    exit 1
}

Write-Log "=== Task Scheduler Setup Completed ===" "INFO"
exit 0
