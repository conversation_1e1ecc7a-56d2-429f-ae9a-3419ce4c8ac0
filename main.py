#!/usr/bin/env python3
"""
Robust Data Storage Service - Main Entry Point
Lightweight orchestrator that delegates to appropriate command handlers.
"""

import sys
import argparse
import time
from datetime import datetime
from pathlib import Path

from src.core.logging import get_logger, setup_enhanced_logging
from src.handlers.command_dispatcher import CommandDispatcher

logger = get_logger(__name__)


def create_argument_parser() -> argparse.ArgumentParser:
    """Create and configure the argument parser."""
    parser = argparse.ArgumentParser(description="Financial Data Service - Real-time data storage and retrieval")

    # Core operations
    parser.add_argument("--api", action="store_true",
                       help="Start API server")
    parser.add_argument("--init-db", action="store_true",
                       help="Initialize database schema")

    # Data operations
    parser.add_argument("--fetch-data", action="store_true",
                       help="Fetch historical data for specified symbols")
    parser.add_argument("--bulk-all-markets", action="store_true",
                       help="Fetch data for all market types (EQUITY, INDEX, FUTURES, OPTIONS)")
    parser.add_argument("--symbols", nargs='+',
                       help="Specific symbols to process (e.g., --symbols ABAN-EQ DJIA-INDEX)")
    parser.add_argument("--days", type=int, default=10,
                       help="Number of days of historical data to fetch (default: 10)")
    parser.add_argument("--start-date", type=str,
                       help="Start date for data fetching (YYYY-MM-DD format)")
    parser.add_argument("--end-date", type=str,
                       help="End date for data fetching (YYYY-MM-DD format)")

    # Specific symbol operations
    parser.add_argument("--fetch-equity", type=str,
                       help="Fetch data for specific equity symbol (e.g., NSE:RELIANCE-EQ)")
    parser.add_argument("--fetch-index", type=str,
                       help="Fetch data for specific index symbol (e.g., NSE:NIFTY50-INDEX)")
    parser.add_argument("--fetch-futures", type=str,
                       help="Fetch data for specific futures symbol (e.g., NSE:RELIANCE25JULFUT)")
    parser.add_argument("--fetch-options", type=str,
                       help="Fetch data for specific options symbol (e.g., NSE:NIFTY25JUL25000CE)")
    parser.add_argument("--market-type", type=str, choices=['EQUITY', 'INDEX', 'FUTURES', 'OPTIONS'],
                       help="Market type for symbol operations")
    parser.add_argument("--auto-all-symbols", action="store_true",
                       help="Automatically fetch data for all symbols in symbol_mapping table")
    parser.add_argument("--limit", type=int,
                       help="Limit number of symbols to process (for testing)")
    parser.add_argument("--resume-from", type=int, default=0,
                       help="Resume processing from specific index (for error recovery)")
    parser.add_argument("--auto-resume", action="store_true",
                       help="Automatically detect and resume from last processed position")

    # OPTIONS-specific arguments (only applicable when --market-type OPTIONS)
    parser.add_argument("--expiry-type", choices=['WEEKLY', 'MONTHLY'],
                       help="Filter OPTIONS by expiry type (WEEKLY or MONTHLY)")
    parser.add_argument("--expiry-month", nargs='+',
                       help="Filter OPTIONS by expiry months (e.g., --expiry-month JUL AUG SEP)")
    parser.add_argument("--strike-range", type=int, default=30,
                       help="Strike price range from spot price for OPTIONS (default: 30)")

    # Maintenance operations
    parser.add_argument("--process-nse-symbols", action="store_true",
                       help="Download and process NSE symbols")
    parser.add_argument("--validate-data-integrity", action="store_true",
                       help="Validate data integrity")
    parser.add_argument("--view-data", action="store_true",
                       help="Display data availability summary")

    # Data management operations
    parser.add_argument("--fix-all-data-issues", action="store_true",
                       help="Comprehensive fix for all data issues (duplicates, missing symbols, etc.)")
    parser.add_argument("--fix-market-type-tables", action="store_true",
                       help="Fix all market type tables (equity_ohlcv, index_ohlcv, futures_ohlcv, options_ohlcv)")
    parser.add_argument("--fix-fyers-symbols", action="store_true",
                       help="Fix null fyers_symbol values using exact examples")
    parser.add_argument("--remove-duplicates", action="store_true",
                       help="Remove duplicate entries from NSE raw tables")
    parser.add_argument("--data-health-report", action="store_true",
                       help="Generate comprehensive data health report")
    parser.add_argument("--no-backup", action="store_true",
                       help="Skip creating backup tables (use with caution)")

    return parser


def main():
    """Main execution function with streamlined orchestration."""
    # Initialize enhanced logging first
    setup_enhanced_logging()

    # Track execution time
    start_time = time.time()
    logger.info(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    try:
        # Parse arguments
        parser = create_argument_parser()
        args = parser.parse_args()

        # Initialize command dispatcher
        dispatcher = CommandDispatcher()

        # Dispatch command to appropriate handler
        success = dispatcher.dispatch(args)

        # Log completion time
        end_time = time.time()
        duration = end_time - start_time
        logger.info(f"⏰ Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"⏱️  Total execution time: {duration:.2f} seconds")

        return success

    except KeyboardInterrupt:
        logger.info("\n⚠️  Operation interrupted by user")
        return False
    except Exception as e:
        logger.error(f"\n❌ Unexpected error: {e}")
        return False


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)
