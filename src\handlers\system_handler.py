"""
Handler for system operations like API server and database initialization.
"""

from typing import Any

from src.handlers.base_handler import BaseCommandHandler
from src.database.connection import init_database
from src.services.bulk_data_service import BulkDataService
from src.database.models import MarketType
from src.core.logging import get_logger

logger = get_logger(__name__)


class SystemHandler(BaseCommandHandler):
    """Handler for system-level operations."""
    
    def __init__(self):
        super().__init__()
        self.bulk_service = BulkDataService()
    
    def execute(self, args: Any) -> bool:
        """Execute system operation based on arguments."""
        try:
            if args.api:
                return self._handle_api_server()
            elif args.init_db:
                return self._handle_init_database()
            elif args.view_data:
                return self._handle_view_data(args)
            else:
                self.logger.error("❌ No valid system operation specified")
                return False
                
        except Exception as e:
            return self.handle_error(e, "System operation")
    
    def _handle_api_server(self) -> bool:
        """Handle API server startup."""
        try:
            self.logger.info("🚀 Starting Robust Data Storage Service API server...")
            from src.api.server import start_server
            start_server()
            return True
            
        except Exception as e:
            return self.handle_error(e, "API server startup")
    
    def _handle_init_database(self) -> bool:
        """Handle database initialization."""
        try:
            self.logger.info("🔧 Initializing database schema...")
            init_database()
            return self.log_success("Database initialization completed")
            
        except Exception as e:
            return self.handle_error(e, "Database initialization")
    
    def _handle_view_data(self, args: Any) -> bool:
        """Handle data availability summary."""
        try:
            self.logger.info("📊 Data Availability Summary")
            self.logger.info("=" * 50)
            
            symbols = args.symbols if hasattr(args, 'symbols') and args.symbols else None
            
            for market_type in [MarketType.EQUITY, MarketType.INDEX, MarketType.FUTURES, MarketType.OPTIONS]:
                self.logger.info(f"\n📈 {market_type.value} Market:")
                summary = self.bulk_service.get_data_summary(market_type, symbols)
                
                if 'error' in summary:
                    self.logger.error(f"   ❌ Error: {summary['error']}")
                elif summary['total_records'] == 0:
                    self.logger.info(f"   📭 No data available")
                else:
                    self.logger.info(f"   📊 Records: {summary['total_records']:,}")
                    self.logger.info(f"   🏷️  Symbols: {summary['symbols_count']}")
                    if summary['date_range']:
                        self.logger.info(f"   📅 Period: {summary['date_range']['start']} to {summary['date_range']['end']}")
            
            return self.log_success("Data summary completed successfully")
            
        except Exception as e:
            return self.handle_error(e, "Data summary generation")
