"""
Base command handler with common functionality.
"""

import time
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Any, Dict, Optional
from src.core.logging import get_logger

logger = get_logger(__name__)


class BaseCommandHandler(ABC):
    """Base class for all command handlers."""
    
    def __init__(self):
        self.start_time = time.time()
        self.logger = logger
    
    @abstractmethod
    def execute(self, args: Any) -> bool:
        """Execute the command with given arguments."""
        pass
    
    def log_execution_time(self, operation_name: str) -> None:
        """Log the execution time for an operation."""
        end_time = time.time()
        duration = end_time - self.start_time
        self.logger.info(f"⏱️  {operation_name} completed in {duration:.2f} seconds")
    
    def handle_error(self, error: Exception, operation_name: str) -> bool:
        """Standard error handling for commands."""
        self.logger.error(f"❌ {operation_name} failed: {error}")
        return False
    
    def log_success(self, message: str) -> bool:
        """Log success message and return True."""
        self.logger.info(f"✅ {message}")
        return True
    
    def validate_required_args(self, args: Any, required_fields: list) -> bool:
        """Validate that required arguments are present."""
        for field in required_fields:
            if not hasattr(args, field) or getattr(args, field) is None:
                self.logger.error(f"❌ Required argument --{field.replace('_', '-')} is missing")
                return False
        return True
