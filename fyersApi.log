{"level":"ERROR","location":"[get_call:118] fyersModel","message":"HTTPSConnectionPool(host='api-t1.fyers.in', port=443): Max retries exceeded with url: /data/history?symbol=NSE%3AHINDZINC25SEPFUT&resolution=1&date_format=1&range_from=2025-04-01&range_to=2025-06-30&cont_flag=1 (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001F3CB5EB4F0>, 'Connection to api-t1.fyers.in timed out. (connect timeout=None)'))","timestamp":"2025-07-28 04:50:44,723+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"HTTPSConnectionPool(host='api-t1.fyers.in', port=443): Max retries exceeded with url: /data/history?symbol=NSE%3AHINDZINC25SEPFUT&resolution=1&date_format=1&range_from=2025-07-01&range_to=2025-07-25&cont_flag=1 (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001F3CB5EAE30>, 'Connection to api-t1.fyers.in timed out. (connect timeout=None)'))","timestamp":"2025-07-28 04:51:28,814+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"HTTPSConnectionPool(host='api-t1.fyers.in', port=443): Max retries exceeded with url: /data/history?symbol=NSE%3AHINDZINC25SEPFUT&resolution=1&date_format=1&range_from=2025-04-01&range_to=2025-06-30&cont_flag=1 (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001F3B6CD95D0>, 'Connection to api-t1.fyers.in timed out. (connect timeout=None)'))","timestamp":"2025-07-28 04:52:14,962+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"HTTPSConnectionPool(host='api-t1.fyers.in', port=443): Max retries exceeded with url: /data/history?symbol=NSE%3AHINDZINC25SEPFUT&resolution=1&date_format=1&range_from=2025-07-01&range_to=2025-07-25&cont_flag=1 (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001F3B6CDA020>, 'Connection to api-t1.fyers.in timed out. (connect timeout=None)'))","timestamp":"2025-07-28 04:52:59,068+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"HTTPSConnectionPool(host='api-t1.fyers.in', port=443): Max retries exceeded with url: /data/history?symbol=NSE%3AHINDZINC25SEPFUT&resolution=1&date_format=1&range_from=2025-04-01&range_to=2025-06-30&cont_flag=1 (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001F3B6CDA950>: Failed to establish a new connection: [WinError 10053] An established connection was aborted by the software in your host machine'))","timestamp":"2025-07-28 04:53:36,453+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"HTTPSConnectionPool(host='api-t1.fyers.in', port=443): Max retries exceeded with url: /data/history?symbol=NSE%3AHINDZINC25SEPFUT&resolution=1&date_format=1&range_from=2025-07-01&range_to=2025-07-25&cont_flag=1 (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x000001F3B6CDB070>: Failed to resolve 'api-t1.fyers.in' ([Errno 11001] getaddrinfo failed)\"))","timestamp":"2025-07-28 04:53:38,524+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"HTTPSConnectionPool(host='api-t1.fyers.in', port=443): Max retries exceeded with url: /data/history?symbol=NSE%3AHINDZINC25SEPFUT&resolution=1&date_format=1&range_from=2025-04-01&range_to=2025-06-30&cont_flag=1 (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x000001F3CB5EB460>: Failed to resolve 'api-t1.fyers.in' ([Errno 11001] getaddrinfo failed)\"))","timestamp":"2025-07-28 04:53:48,567+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"HTTPSConnectionPool(host='api-t1.fyers.in', port=443): Max retries exceeded with url: /data/history?symbol=NSE%3AHINDZINC25SEPFUT&resolution=1&date_format=1&range_from=2025-07-01&range_to=2025-07-25&cont_flag=1 (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x000001F3CB5EB730>: Failed to resolve 'api-t1.fyers.in' ([Errno 11001] getaddrinfo failed)\"))","timestamp":"2025-07-28 04:53:50,582+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"HTTPSConnectionPool(host='api-t1.fyers.in', port=443): Max retries exceeded with url: /data/history?symbol=NSE%3AHINDZINC25SEPFUT&resolution=1&date_format=1&range_from=2025-04-01&range_to=2025-06-30&cont_flag=1 (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x000001F3B6CDAC20>: Failed to resolve 'api-t1.fyers.in' ([Errno 11001] getaddrinfo failed)\"))","timestamp":"2025-07-28 04:54:08,613+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"HTTPSConnectionPool(host='api-t1.fyers.in', port=443): Max retries exceeded with url: /data/history?symbol=NSE%3AHINDZINC25SEPFUT&resolution=1&date_format=1&range_from=2025-07-01&range_to=2025-07-25&cont_flag=1 (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x000001F3B6CDAE90>: Failed to resolve 'api-t1.fyers.in' ([Errno 11001] getaddrinfo failed)\"))","timestamp":"2025-07-28 04:54:10,626+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"HTTPSConnectionPool(host='api-t1.fyers.in', port=443): Max retries exceeded with url: /data/history?symbol=NSE%3AHUDCO25AUGFUT&resolution=1&date_format=1&range_from=2025-04-01&range_to=2025-06-30&cont_flag=1 (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x000001F3B6CDA3E0>: Failed to resolve 'api-t1.fyers.in' ([Errno 11001] getaddrinfo failed)\"))","timestamp":"2025-07-28 04:54:12,790+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"HTTPSConnectionPool(host='api-t1.fyers.in', port=443): Max retries exceeded with url: /data/history?symbol=NSE%3AHUDCO25AUGFUT&resolution=1&date_format=1&range_from=2025-07-01&range_to=2025-07-25&cont_flag=1 (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x000001F3CB5EB8B0>: Failed to resolve 'api-t1.fyers.in' ([Errno 11001] getaddrinfo failed)\"))","timestamp":"2025-07-28 04:54:14,813+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"HTTPSConnectionPool(host='api-t1.fyers.in', port=443): Max retries exceeded with url: /data/history?symbol=NSE%3AHUDCO25AUGFUT&resolution=1&date_format=1&range_from=2025-04-01&range_to=2025-06-30&cont_flag=1 (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x000001F3CB5EAF50>: Failed to resolve 'api-t1.fyers.in' ([Errno 11001] getaddrinfo failed)\"))","timestamp":"2025-07-28 04:54:18,851+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"HTTPSConnectionPool(host='api-t1.fyers.in', port=443): Max retries exceeded with url: /data/history?symbol=NSE%3AHUDCO25AUGFUT&resolution=1&date_format=1&range_from=2025-07-01&range_to=2025-07-25&cont_flag=1 (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x000001F3CB5EABC0>: Failed to resolve 'api-t1.fyers.in' ([Errno 11001] getaddrinfo failed)\"))","timestamp":"2025-07-28 04:54:20,869+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"HTTPSConnectionPool(host='api-t1.fyers.in', port=443): Max retries exceeded with url: /data/history?symbol=NSE%3AHUDCO25AUGFUT&resolution=1&date_format=1&range_from=2025-04-01&range_to=2025-06-30&cont_flag=1 (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x000001F3B6CDA9E0>: Failed to resolve 'api-t1.fyers.in' ([Errno 11001] getaddrinfo failed)\"))","timestamp":"2025-07-28 04:54:26,896+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"HTTPSConnectionPool(host='api-t1.fyers.in', port=443): Max retries exceeded with url: /data/history?symbol=NSE%3AHUDCO25AUGFUT&resolution=1&date_format=1&range_from=2025-07-01&range_to=2025-07-25&cont_flag=1 (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x000001F3B6CDAA40>: Failed to resolve 'api-t1.fyers.in' ([Errno 11001] getaddrinfo failed)\"))","timestamp":"2025-07-28 04:54:28,916+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"HTTPSConnectionPool(host='api-t1.fyers.in', port=443): Max retries exceeded with url: /data/history?symbol=NSE%3AHUDCO25AUGFUT&resolution=1&date_format=1&range_from=2025-04-01&range_to=2025-06-30&cont_flag=1 (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x000001F3B6CD9B70>: Failed to resolve 'api-t1.fyers.in' ([Errno 11001] getaddrinfo failed)\"))","timestamp":"2025-07-28 04:54:39,104+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"HTTPSConnectionPool(host='api-t1.fyers.in', port=443): Max retries exceeded with url: /data/history?symbol=NSE%3AHUDCO25AUGFUT&resolution=1&date_format=1&range_from=2025-07-01&range_to=2025-07-25&cont_flag=1 (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x000001F3CB5EB190>: Failed to resolve 'api-t1.fyers.in' ([Errno 11001] getaddrinfo failed)\"))","timestamp":"2025-07-28 04:54:41,121+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"HTTPSConnectionPool(host='api-t1.fyers.in', port=443): Max retries exceeded with url: /data/history?symbol=NSE%3AHUDCO25AUGFUT&resolution=1&date_format=1&range_from=2025-04-01&range_to=2025-06-30&cont_flag=1 (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x000001F3CB5EBF10>: Failed to resolve 'api-t1.fyers.in' ([Errno 11001] getaddrinfo failed)\"))","timestamp":"2025-07-28 04:54:59,153+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"HTTPSConnectionPool(host='api-t1.fyers.in', port=443): Max retries exceeded with url: /data/history?symbol=NSE%3AHUDCO25AUGFUT&resolution=1&date_format=1&range_from=2025-07-01&range_to=2025-07-25&cont_flag=1 (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x000001F3B6CDB430>: Failed to resolve 'api-t1.fyers.in' ([Errno 11001] getaddrinfo failed)\"))","timestamp":"2025-07-28 04:55:01,181+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"HTTPSConnectionPool(host='api-t1.fyers.in', port=443): Max retries exceeded with url: /data/history?symbol=NSE%3AHUDCO25JULFUT&resolution=1&date_format=1&range_from=2025-04-01&range_to=2025-06-30&cont_flag=1 (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x000001F3B6CDA9E0>: Failed to resolve 'api-t1.fyers.in' ([Errno 11001] getaddrinfo failed)\"))","timestamp":"2025-07-28 04:55:03,203+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"HTTPSConnectionPool(host='api-t1.fyers.in', port=443): Max retries exceeded with url: /data/history?symbol=NSE%3AHUDCO25JULFUT&resolution=1&date_format=1&range_from=2025-07-01&range_to=2025-07-25&cont_flag=1 (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x000001F3B6CDA560>: Failed to resolve 'api-t1.fyers.in' ([Errno 11001] getaddrinfo failed)\"))","timestamp":"2025-07-28 04:55:05,220+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"HTTPSConnectionPool(host='api-t1.fyers.in', port=443): Max retries exceeded with url: /data/history?symbol=NSE%3AHUDCO25JULFUT&resolution=1&date_format=1&range_from=2025-04-01&range_to=2025-06-30&cont_flag=1 (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x000001F3CB5EAEC0>: Failed to resolve 'api-t1.fyers.in' ([Errno 11001] getaddrinfo failed)\"))","timestamp":"2025-07-28 04:55:09,258+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"HTTPSConnectionPool(host='api-t1.fyers.in', port=443): Max retries exceeded with url: /data/history?symbol=NSE%3AHUDCO25JULFUT&resolution=1&date_format=1&range_from=2025-07-01&range_to=2025-07-25&cont_flag=1 (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x000001F3CB5EA9B0>: Failed to resolve 'api-t1.fyers.in' ([Errno 11001] getaddrinfo failed)\"))","timestamp":"2025-07-28 04:55:11,282+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"HTTPSConnectionPool(host='api-t1.fyers.in', port=443): Max retries exceeded with url: /data/history?symbol=NSE%3AHUDCO25JULFUT&resolution=1&date_format=1&range_from=2025-04-01&range_to=2025-06-30&cont_flag=1 (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x000001F3B6CDA0E0>: Failed to resolve 'api-t1.fyers.in' ([Errno 11001] getaddrinfo failed)\"))","timestamp":"2025-07-28 04:55:17,326+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"HTTPSConnectionPool(host='api-t1.fyers.in', port=443): Max retries exceeded with url: /data/history?symbol=NSE%3AHUDCO25JULFUT&resolution=1&date_format=1&range_from=2025-07-01&range_to=2025-07-25&cont_flag=1 (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x000001F3B6CDAE00>: Failed to resolve 'api-t1.fyers.in' ([Errno 11001] getaddrinfo failed)\"))","timestamp":"2025-07-28 04:55:19,343+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"HTTPSConnectionPool(host='api-t1.fyers.in', port=443): Max retries exceeded with url: /data/history?symbol=NSE%3AHUDCO25JULFUT&resolution=1&date_format=1&range_from=2025-04-01&range_to=2025-06-30&cont_flag=1 (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x000001F3B6CDADD0>: Failed to resolve 'api-t1.fyers.in' ([Errno 11001] getaddrinfo failed)\"))","timestamp":"2025-07-28 04:55:29,385+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"HTTPSConnectionPool(host='api-t1.fyers.in', port=443): Max retries exceeded with url: /data/history?symbol=NSE%3AHUDCO25JULFUT&resolution=1&date_format=1&range_from=2025-07-01&range_to=2025-07-25&cont_flag=1 (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x000001F3CB5EB280>: Failed to resolve 'api-t1.fyers.in' ([Errno 11001] getaddrinfo failed)\"))","timestamp":"2025-07-28 04:55:31,396+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"HTTPSConnectionPool(host='api-t1.fyers.in', port=443): Max retries exceeded with url: /data/history?symbol=NSE%3AHUDCO25JULFUT&resolution=1&date_format=1&range_from=2025-04-01&range_to=2025-06-30&cont_flag=1 (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x000001F3CB5EAF80>: Failed to resolve 'api-t1.fyers.in' ([Errno 11001] getaddrinfo failed)\"))","timestamp":"2025-07-28 04:55:49,430+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"HTTPSConnectionPool(host='api-t1.fyers.in', port=443): Max retries exceeded with url: /data/history?symbol=NSE%3AHUDCO25JULFUT&resolution=1&date_format=1&range_from=2025-07-01&range_to=2025-07-25&cont_flag=1 (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x000001F3CB5EADD0>: Failed to resolve 'api-t1.fyers.in' ([Errno 11001] getaddrinfo failed)\"))","timestamp":"2025-07-28 04:55:51,455+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"HTTPSConnectionPool(host='api-t1.fyers.in', port=443): Max retries exceeded with url: /api/v3/profile (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x000001F3B6CDA0B0>: Failed to resolve 'api-t1.fyers.in' ([Errno 11001] getaddrinfo failed)\"))","timestamp":"2025-07-28 04:55:53,521+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"HTTPSConnectionPool(host='api-t1.fyers.in', port=443): Max retries exceeded with url: /data/history?symbol=NSE%3ANHPC25JULFUT&resolution=1&date_format=1&range_from=2025-04-01&range_to=2025-06-30&cont_flag=1 (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x000001F3CB4474F0>: Failed to resolve 'api-t1.fyers.in' ([Errno 11001] getaddrinfo failed)\"))","timestamp":"2025-07-28 10:58:10,734+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"HTTPSConnectionPool(host='api-t1.fyers.in', port=443): Max retries exceeded with url: /data/history?symbol=NSE%3ANHPC25JULFUT&resolution=1&date_format=1&range_from=2025-07-01&range_to=2025-07-25&cont_flag=1 (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x000001F3CB446E00>: Failed to resolve 'api-t1.fyers.in' ([Errno 11001] getaddrinfo failed)\"))","timestamp":"2025-07-28 10:58:12,749+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"HTTPSConnectionPool(host='api-t1.fyers.in', port=443): Max retries exceeded with url: /data/history?symbol=NSE%3ANEWGEN-EQ&resolution=1&date_format=1&range_from=2025-07-01&range_to=2025-07-28&cont_flag=1 (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000002B3008F97B0>: Failed to establish a new connection: [WinError 10051] A socket operation was attempted to an unreachable network'))","timestamp":"2025-07-28 19:09:10,017+0530","service":"FyersDataSocket"}
{"level":"ERROR","location":"[get_call:118] fyersModel","message":"('Connection aborted.', ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None))","timestamp":"2025-07-29 17:21:44,031+0530","service":"FyersDataSocket"}
