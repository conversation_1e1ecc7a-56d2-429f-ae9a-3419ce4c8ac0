#!/usr/bin/env python3
"""
Comprehensive test suite for OPTIONS functionality including prioritization and spot price services.
"""

import sys
import os
from datetime import datetime

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from src.core.logging import get_logger, setup_enhanced_logging
from src.services.options_prioritizer import OptionsPrioritizer
from src.services.spot_price_service import SpotPriceService

# Setup logging
setup_enhanced_logging()
logger = get_logger(__name__)


class OptionsTestSuite:
    """Comprehensive test suite for OPTIONS functionality."""
    
    def __init__(self):
        """Initialize the test suite."""
        self.prioritizer = OptionsPrioritizer()
        self.spot_service = SpotPriceService()
        self.test_results = {}
    
    def test_spot_price_service(self) -> bool:
        """Test the spot price service functionality."""
        logger.info("🧪 Testing Spot Price Service")
        
        test_symbols = [
            ('NIFTY', 'INDEX'),
            ('BANKNIFTY', 'INDEX'),
            ('RELIANCE', 'EQUITY'),
            ('TCS', 'EQUITY')
        ]
        
        successful = 0
        for symbol, market_type in test_symbols:
            spot_price = self.spot_service.get_spot_price(symbol, market_type)
            if spot_price and spot_price > 0:
                successful += 1
                logger.info(f"✅ {symbol}: ₹{spot_price}")
            else:
                logger.warning(f"❌ {symbol}: No price")
        
        success_rate = successful / len(test_symbols)
        self.test_results['spot_price_service'] = success_rate >= 0.8
        return self.test_results['spot_price_service']
    
    def test_options_prioritizer(self) -> bool:
        """Test the OPTIONS prioritizer functionality."""
        logger.info("🧪 Testing OPTIONS Prioritizer")
        
        # Test basic prioritization
        symbols = self.prioritizer.get_prioritized_options_symbols(limit=20)
        basic_test = len(symbols) > 0
        
        # Test with filters
        monthly_symbols = self.prioritizer.get_prioritized_options_symbols(
            expiry_type='MONTHLY', limit=10
        )
        filter_test = len(monthly_symbols) >= 0  # Could be 0, that's ok
        
        # Test summary generation
        summary = self.prioritizer.get_prioritization_summary(symbols)
        summary_test = 'total' in summary and summary['total'] == len(symbols)
        
        overall_success = basic_test and filter_test and summary_test
        self.test_results['options_prioritizer'] = overall_success
        
        logger.info(f"✅ Basic prioritization: {basic_test}")
        logger.info(f"✅ Filter functionality: {filter_test}")
        logger.info(f"✅ Summary generation: {summary_test}")
        
        return overall_success
    
    def test_performance(self) -> bool:
        """Test performance characteristics."""
        logger.info("🧪 Testing Performance")
        
        import time
        
        # Test with different limits
        limits = [10, 50, 100]
        performance_ok = True
        
        for limit in limits:
            start_time = time.time()
            symbols = self.prioritizer.get_prioritized_options_symbols(limit=limit)
            duration = time.time() - start_time
            
            # Performance should be reasonable (less than 30 seconds for 100 symbols)
            expected_max_time = limit * 0.3  # 0.3 seconds per symbol max
            if duration > expected_max_time:
                performance_ok = False
                logger.warning(f"⚠️ Performance issue: {limit} symbols took {duration:.2f}s")
            else:
                logger.info(f"✅ {limit} symbols in {duration:.2f}s")
        
        self.test_results['performance'] = performance_ok
        return performance_ok
    
    def test_cache_functionality(self) -> bool:
        """Test caching functionality."""
        logger.info("🧪 Testing Cache Functionality")
        
        # Get initial cache stats
        initial_stats = self.prioritizer.get_cache_stats()
        
        # Run prioritization to populate caches
        self.prioritizer.get_prioritized_options_symbols(limit=10)
        
        # Check if caches were populated
        populated_stats = self.prioritizer.get_cache_stats()
        caches_populated = any(size > 0 for size in populated_stats.values())
        
        # Clear caches
        self.prioritizer.clear_caches()
        cleared_stats = self.prioritizer.get_cache_stats()
        caches_cleared = all(size == 0 for size in cleared_stats.values())
        
        cache_test = caches_populated and caches_cleared
        self.test_results['cache_functionality'] = cache_test
        
        logger.info(f"✅ Caches populated: {caches_populated}")
        logger.info(f"✅ Caches cleared: {caches_cleared}")
        
        return cache_test
    
    def run_all_tests(self) -> bool:
        """Run all tests and return overall success."""
        logger.info("🚀 Running OPTIONS Functionality Test Suite")
        logger.info("=" * 60)
        
        tests = [
            self.test_spot_price_service,
            self.test_options_prioritizer,
            self.test_performance,
            self.test_cache_functionality
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            try:
                if test():
                    passed += 1
            except Exception as e:
                logger.error(f"❌ Test failed with exception: {e}")
        
        # Summary
        logger.info(f"\n📊 TEST SUMMARY")
        logger.info("=" * 40)
        logger.info(f"✅ Passed: {passed}/{total} tests")
        
        for test_name, result in self.test_results.items():
            status = "✅" if result else "❌"
            logger.info(f"   {status} {test_name}")
        
        overall_success = passed == total
        if overall_success:
            logger.info("🎉 All tests passed!")
        else:
            logger.warning("⚠️ Some tests failed. Check logs above.")
        
        return overall_success


def main():
    """Main test function."""
    test_suite = OptionsTestSuite()
    success = test_suite.run_all_tests()
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
