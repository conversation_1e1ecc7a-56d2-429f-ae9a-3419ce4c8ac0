"""
FastAPI dependency injection functions.
Provides dependency injection for services and database connections.
"""

from sqlalchemy.orm import Session
from fastapi import Depends
from src.database.connection import get_db
from src.services.data_storage_service import DataStorageService
from src.auth.fyers_integration_service import FyersIntegrationService


def get_data_storage_service(db: Session = Depends(get_db)) -> DataStorageService:
    """
    Get DataStorageService instance.
    
    Args:
        db: Database session
        
    Returns:
        DataStorageService instance
    """
    return DataStorageService(db)


def get_fyers_service() -> FyersIntegrationService:
    """
    Get FyersIntegrationService instance.
    
    Returns:
        FyersIntegrationService instance
    """
    return FyersIntegrationService()
