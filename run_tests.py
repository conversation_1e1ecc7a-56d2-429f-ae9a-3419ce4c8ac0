#!/usr/bin/env python3
"""
Comprehensive test runner for the enhanced OPTIONS functionality.
"""

import sys
import os
import time
from datetime import datetime

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.core.logging import get_logger, setup_enhanced_logging

# Setup logging
setup_enhanced_logging()
logger = get_logger(__name__)


def run_test_module(module_path: str, test_name: str) -> bool:
    """Run a specific test module."""
    try:
        logger.info(f"🧪 Running {test_name}")
        
        # Import and run the test
        spec = __import__(module_path.replace('/', '.').replace('.py', ''), fromlist=['main'])
        
        if hasattr(spec, 'main'):
            result = spec.main()
            if result:
                logger.info(f"✅ {test_name} passed")
                return True
            else:
                logger.error(f"❌ {test_name} failed")
                return False
        else:
            logger.warning(f"⚠️ {test_name} has no main() function")
            return False
            
    except Exception as e:
        logger.error(f"❌ {test_name} failed with exception: {e}")
        return False


def test_main_functionality():
    """Test main.py functionality with various OPTIONS arguments."""
    logger.info("🧪 Testing main.py OPTIONS functionality")
    
    import subprocess
    
    test_commands = [
        # Basic OPTIONS test
        ["python", "main.py", "--auto-all-symbols", "--market-type", "OPTIONS", "--limit", "5", "--days", "1"],
        
        # Monthly OPTIONS test
        ["python", "main.py", "--auto-all-symbols", "--market-type", "OPTIONS", "--expiry-type", "MONTHLY", "--limit", "3", "--days", "1"],
        
        # Specific month test
        ["python", "main.py", "--auto-all-symbols", "--market-type", "OPTIONS", "--expiry-month", "JUL", "--limit", "3", "--days", "1"],
        
        # Combined filters test
        ["python", "main.py", "--auto-all-symbols", "--market-type", "OPTIONS", "--expiry-type", "MONTHLY", "--expiry-month", "JUL", "--strike-range", "20", "--limit", "2", "--days", "1"]
    ]
    
    passed = 0
    total = len(test_commands)
    
    for i, cmd in enumerate(test_commands, 1):
        logger.info(f"🔍 Test {i}/{total}: {' '.join(cmd[2:])}")
        
        try:
            # Run with timeout
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                logger.info(f"✅ Command {i} succeeded")
                passed += 1
            else:
                logger.error(f"❌ Command {i} failed with return code {result.returncode}")
                if result.stderr:
                    logger.error(f"   Error: {result.stderr[:200]}...")
                    
        except subprocess.TimeoutExpired:
            logger.error(f"❌ Command {i} timed out")
        except Exception as e:
            logger.error(f"❌ Command {i} failed with exception: {e}")
    
    success_rate = passed / total
    logger.info(f"📊 Main functionality tests: {passed}/{total} passed ({success_rate:.1%})")
    
    return success_rate >= 0.75  # 75% success rate required


def main():
    """Main test runner."""
    logger.info("🚀 Enhanced OPTIONS Functionality Test Suite")
    logger.info("=" * 80)
    
    start_time = time.time()
    
    # Test modules to run
    test_modules = [
        ("tests/test_options_functionality.py", "OPTIONS Core Functionality"),
        ("tests/test_enhanced_symbol_processing.py", "Enhanced Symbol Processing"),
        ("tests/test_reliance_nifty_data_flow.py", "RELIANCE/NIFTY Data Flow")
    ]
    
    # Run individual test modules
    module_results = []
    for module_path, test_name in test_modules:
        if os.path.exists(module_path):
            result = run_test_module(module_path, test_name)
            module_results.append((test_name, result))
        else:
            logger.warning(f"⚠️ Test module not found: {module_path}")
            module_results.append((test_name, False))
    
    # Test main.py functionality
    main_test_result = test_main_functionality()
    module_results.append(("Main.py OPTIONS Functionality", main_test_result))
    
    # Overall summary
    total_duration = time.time() - start_time
    passed_tests = sum(1 for _, result in module_results if result)
    total_tests = len(module_results)
    
    logger.info(f"\n🎉 FINAL TEST SUMMARY")
    logger.info("=" * 60)
    logger.info(f"⏱️  Total duration: {total_duration:.2f}s")
    logger.info(f"✅ Passed: {passed_tests}/{total_tests} test suites")
    logger.info(f"📊 Success rate: {passed_tests/total_tests:.1%}")
    
    logger.info(f"\n📋 Detailed Results:")
    for test_name, result in module_results:
        status = "✅" if result else "❌"
        logger.info(f"   {status} {test_name}")
    
    overall_success = passed_tests == total_tests
    
    if overall_success:
        logger.info("\n🎉 ALL TESTS PASSED! 🎉")
        logger.info("The enhanced OPTIONS functionality is working correctly.")
    else:
        logger.warning(f"\n⚠️ {total_tests - passed_tests} test suite(s) failed.")
        logger.info("Check the logs above for details.")
    
    # Recommendations
    logger.info(f"\n💡 RECOMMENDATIONS:")
    if overall_success:
        logger.info("✅ The system is ready for production use with OPTIONS prioritization")
        logger.info("✅ All new CLI arguments are working correctly")
        logger.info("✅ Performance optimizations are effective")
    else:
        logger.info("🔧 Review failed tests and fix issues before production deployment")
        logger.info("🔧 Consider running individual test modules for detailed debugging")
    
    logger.info(f"\n📚 USAGE EXAMPLES:")
    logger.info("# Basic OPTIONS processing with prioritization:")
    logger.info("python main.py --auto-all-symbols --market-type OPTIONS --limit 10 --days 1")
    logger.info("")
    logger.info("# Monthly OPTIONS for specific months:")
    logger.info("python main.py --auto-all-symbols --market-type OPTIONS --expiry-type MONTHLY --expiry-month JUL AUG --limit 20 --days 1")
    logger.info("")
    logger.info("# Weekly OPTIONS with custom strike range:")
    logger.info("python main.py --auto-all-symbols --market-type OPTIONS --expiry-type WEEKLY --strike-range 20 --limit 15 --days 1")
    
    return overall_success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
