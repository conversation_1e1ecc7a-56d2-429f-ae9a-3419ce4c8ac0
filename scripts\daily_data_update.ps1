# Daily Data Update Script for Simple Data Service
# This script performs complete end-to-end data updates for all market types
# Designed to run daily via Windows Task Scheduler

param(
    [string]$LogLevel = "INFO",
    [switch]$DryRun = $false,
    [string]$ConfigPath = "config.yaml"
)

# Script configuration
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectRoot = Split-Path -Parent $ScriptDir
$LogDir = Join-Path $ProjectRoot "logs"
$VenvPath = Join-Path $ProjectRoot "venv\Scripts\Activate.ps1"
$MainScript = Join-Path $ProjectRoot "main.py"

# Ensure logs directory exists
if (-not (Test-Path $LogDir)) {
    New-Item -ItemType Directory -Path $LogDir -Force | Out-Null
}

# Setup logging
$LogFile = Join-Path $LogDir "daily_update_$(Get-Date -Format 'yyyyMMdd_HHmmss').log"
$ErrorLogFile = Join-Path $LogDir "daily_update_errors_$(Get-Date -Format 'yyyyMMdd_HHmmss').log"

function Write-Log {
    param(
        [string]$Message,
        [string]$Level = "INFO"
    )
    $Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $LogEntry = "$Timestamp | $Level | $Message"
    Write-Host $LogEntry
    Add-Content -Path $LogFile -Value $LogEntry
    
    if ($Level -eq "ERROR") {
        Add-Content -Path $ErrorLogFile -Value $LogEntry
    }
}

function Test-PythonEnvironment {
    Write-Log "Testing Python environment..." "INFO"
    
    if (-not (Test-Path $VenvPath)) {
        Write-Log "Virtual environment not found at: $VenvPath" "ERROR"
        return $false
    }
    
    try {
        & $VenvPath
        $pythonVersion = python --version 2>&1
        Write-Log "Python environment activated: $pythonVersion" "INFO"
        return $true
    }
    catch {
        Write-Log "Failed to activate Python environment: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Update-NSESymbols {
    Write-Log "Starting NSE symbol processing..." "INFO"
    
    try {
        if ($DryRun) {
            Write-Log "[DRY RUN] Would process NSE symbols" "INFO"
            return $true
        }
        
        $result = python $MainScript --process-nse-symbols 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Log "NSE symbol processing completed successfully" "INFO"
            return $true
        } else {
            Write-Log "NSE symbol processing failed with exit code: $LASTEXITCODE" "ERROR"
            Write-Log "Output: $result" "ERROR"
            return $false
        }
    }
    catch {
        Write-Log "Exception during NSE symbol processing: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Update-MarketData {
    param(
        [string]$MarketType,
        [int]$DaysBack = 1,
        [int]$SymbolLimit = 0  # 0 means no limit
    )

    Write-Log "Starting data update for $MarketType market..." "INFO"

    try {
        if ($DryRun) {
            Write-Log "[DRY RUN] Would update $MarketType data for last $DaysBack days" "INFO"
            return $true
        }

        $startDate = (Get-Date).AddDays(-$DaysBack).ToString("yyyy-MM-dd")
        $endDate = (Get-Date).ToString("yyyy-MM-dd")

        $arguments = @(
            $MainScript,
            "--auto-all-symbols",
            "--market-type", $MarketType,
            "--start-date", $startDate,
            "--end-date", $endDate
        )

        # Add symbol limit if specified
        if ($SymbolLimit -gt 0) {
            $arguments += @("--limit", $SymbolLimit)
        }

        # Add OPTIONS specific filters for better performance
        if ($MarketType -eq "OPTIONS") {
            $arguments += @("--expiry-month", (Get-Date).ToString("MMM").ToUpper())
        }

        Write-Log "Executing: python $($arguments -join ' ')" "INFO"
        $result = & python @arguments 2>&1

        if ($LASTEXITCODE -eq 0) {
            Write-Log "$MarketType data update completed successfully" "INFO"
            return $true
        } else {
            Write-Log "$MarketType data update failed with exit code: $LASTEXITCODE" "ERROR"
            Write-Log "Output: $result" "ERROR"
            return $false
        }
    }
    catch {
        Write-Log "Exception during $MarketType data update: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Check-DataGaps {
    Write-Log "Checking for data gaps across all market types..." "INFO"

    try {
        if ($DryRun) {
            Write-Log "[DRY RUN] Would check for data gaps" "INFO"
            return $true
        }

        # Generate gap filling report
        $arguments = @(
            $MainScript,
            "--data-health-report"
        )

        $result = & python @arguments 2>&1

        if ($LASTEXITCODE -eq 0) {
            Write-Log "Data gap analysis completed successfully" "INFO"
            return $true
        } else {
            Write-Log "Data gap analysis failed with exit code: $LASTEXITCODE" "WARNING"
            return $false
        }
    }
    catch {
        Write-Log "Exception during data gap analysis: $($_.Exception.Message)" "WARNING"
        return $false
    }
}

function Generate-DataHealthReport {
    Write-Log "Generating data health report..." "INFO"
    
    try {
        if ($DryRun) {
            Write-Log "[DRY RUN] Would generate data health report" "INFO"
            return $true
        }
        
        $result = python $MainScript --data-health-report 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Log "Data health report generated successfully" "INFO"
            return $true
        } else {
            Write-Log "Data health report generation failed with exit code: $LASTEXITCODE" "ERROR"
            Write-Log "Output: $result" "ERROR"
            return $false
        }
    }
    catch {
        Write-Log "Exception during data health report generation: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Send-NotificationEmail {
    param(
        [string]$Subject,
        [string]$Body,
        [string]$Priority = "Normal"
    )
    
    # Email configuration (customize as needed)
    $SmtpServer = "smtp.gmail.com"
    $SmtpPort = 587
    $From = "<EMAIL>"
    $To = "<EMAIL>"
    $Username = "<EMAIL>"
    $Password = "your-app-password"  # Use app password for Gmail
    
    try {
        if ($DryRun) {
            Write-Log "[DRY RUN] Would send email: $Subject" "INFO"
            return
        }
        
        $SecurePassword = ConvertTo-SecureString $Password -AsPlainText -Force
        $Credential = New-Object System.Management.Automation.PSCredential($Username, $SecurePassword)
        
        Send-MailMessage -SmtpServer $SmtpServer -Port $SmtpPort -UseSsl -Credential $Credential `
                        -From $From -To $To -Subject $Subject -Body $Body -Priority $Priority
        
        Write-Log "Notification email sent successfully" "INFO"
    }
    catch {
        Write-Log "Failed to send notification email: $($_.Exception.Message)" "ERROR"
    }
}

# Main execution
Write-Log "=== Daily Data Update Script Started ===" "INFO"
Write-Log "Script Parameters: LogLevel=$LogLevel, DryRun=$DryRun, ConfigPath=$ConfigPath" "INFO"

$overallSuccess = $true
$marketTypes = @("EQUITY", "INDEX", "FUTURES", "OPTIONS")
$successfulMarkets = @()
$failedMarkets = @()

try {
    # Step 1: Test Python environment
    if (-not (Test-PythonEnvironment)) {
        throw "Python environment test failed"
    }
    
    # Step 2: Update NSE symbols
    if (-not (Update-NSESymbols)) {
        Write-Log "NSE symbol update failed, but continuing with data updates..." "WARNING"
    }
    
    # Step 3: Check for data gaps first
    Check-DataGaps

    # Step 4: Update data for each market type with smart limits
    $marketLimits = @{
        "EQUITY" = 100      # Limit for daily updates
        "INDEX" = 50        # Smaller set, process all
        "FUTURES" = 75      # Medium priority
        "OPTIONS" = 50      # High volume, limit for performance
    }

    foreach ($marketType in $marketTypes) {
        Write-Log "Processing $marketType market type..." "INFO"

        $symbolLimit = $marketLimits[$marketType]
        if (Update-MarketData -MarketType $marketType -DaysBack 2 -SymbolLimit $symbolLimit) {
            $successfulMarkets += $marketType
            Write-Log "$marketType processing completed successfully" "INFO"
        } else {
            $failedMarkets += $marketType
            $overallSuccess = $false
            Write-Log "$marketType processing failed" "ERROR"
        }

        # Small delay between market types to prevent overwhelming the system
        Start-Sleep -Seconds 10
    }
    
    # Step 4: Generate data health report
    if (-not (Generate-DataHealthReport)) {
        Write-Log "Data health report generation failed" "WARNING"
    }
    
    # Step 5: Summary and notifications
    $successCount = $successfulMarkets.Count
    $failCount = $failedMarkets.Count
    $totalCount = $marketTypes.Count
    
    Write-Log "=== Daily Update Summary ===" "INFO"
    Write-Log "Total market types: $totalCount" "INFO"
    Write-Log "Successful: $successCount ($($successfulMarkets -join ', '))" "INFO"
    Write-Log "Failed: $failCount ($($failedMarkets -join ', '))" "INFO"
    Write-Log "Overall success: $overallSuccess" "INFO"
    
    # Send notification email
    $emailSubject = "Daily Data Update - $(if ($overallSuccess) { 'SUCCESS' } else { 'PARTIAL FAILURE' })"
    $emailBody = @"
Daily Data Update Report - $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')

Summary:
- Total market types processed: $totalCount
- Successful: $successCount ($($successfulMarkets -join ', '))
- Failed: $failCount ($($failedMarkets -join ', '))
- Overall status: $(if ($overallSuccess) { 'SUCCESS' } else { 'PARTIAL FAILURE' })

Log files:
- Main log: $LogFile
- Error log: $ErrorLogFile

Please check the logs for detailed information.
"@
    
    $priority = if ($overallSuccess) { "Normal" } else { "High" }
    Send-NotificationEmail -Subject $emailSubject -Body $emailBody -Priority $priority
    
} catch {
    $overallSuccess = $false
    Write-Log "Critical error in daily update script: $($_.Exception.Message)" "ERROR"
    
    # Send critical error notification
    Send-NotificationEmail -Subject "Daily Data Update - CRITICAL ERROR" `
                          -Body "Critical error occurred during daily data update: $($_.Exception.Message)`n`nLog file: $LogFile" `
                          -Priority "High"
}

Write-Log "=== Daily Data Update Script Completed ===" "INFO"
Write-Log "Final status: $(if ($overallSuccess) { 'SUCCESS' } else { 'FAILURE' })" "INFO"

# Exit with appropriate code
exit $(if ($overallSuccess) { 0 } else { 1 })
