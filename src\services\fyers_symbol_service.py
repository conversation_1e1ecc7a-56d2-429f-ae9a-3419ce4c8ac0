"""
Fyers Symbol Service - Handles proper Fyers symbol construction.
Implements exact patterns as specified by the user.
"""

import logging
import re
from typing import Optional, Dict, Any
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import and_

from src.database.connection import get_db
from src.database.models import SymbolMapping, MarketType

logger = logging.getLogger(__name__)


class FyersSymbolService:
    """Service for constructing and managing Fyers symbols with exact patterns."""
    
    def __init__(self):
        """Initialize the Fyers symbol service."""
        self.db: Session = next(get_db())
    
    def __del__(self):
        """Clean up database connection."""
        if hasattr(self, 'db'):
            self.db.close()
    
    def construct_fyers_symbol(self, nse_symbol: str, market_type: MarketType, 
                              expiry_date: Optional[str] = None, 
                              strike_price: Optional[float] = None,
                              option_type: Optional[str] = None) -> str:
        """
        Construct Fyers symbol using exact patterns specified by user.
        
        Patterns:
        - EQUITY: NSE:UNDERLYING-EQ (e.g., "NSE:RELIANCE-EQ")
        - INDEX: NSE:UNDERLYING-INDEX (e.g., "NSE:NIFTY50-INDEX")
        - FUTURES: NSE:UNDERLYINGYYMMMFUT (e.g., "NSE:RELIANCE25JULFUT")
        - OPTIONS monthly: NSE:UNDERLYINGYYMMMSTRIKECE/PE (e.g., "NSE:RELIANCE25JUL2500CE")
        - OPTIONS weekly: NSE:UNDERLYINGYYMDDSTRIKECE/PE (e.g., "NSE:NIFTY2572425050CE")
        
        Args:
            nse_symbol: Base NSE symbol
            market_type: Market type enum
            expiry_date: Expiry date for futures/options (YYYY-MM-DD format)
            strike_price: Strike price for options
            option_type: Option type (CE/PE) for options
            
        Returns:
            Properly formatted Fyers symbol
        """
        try:
            # Clean the NSE symbol
            clean_symbol = nse_symbol.strip().upper()
            
            # Remove any existing suffixes to get underlying
            underlying = self._extract_underlying(clean_symbol, market_type)
            
            if market_type == MarketType.EQUITY:
                return f"NSE:{underlying}-EQ"
            
            elif market_type == MarketType.INDEX:
                return f"NSE:{underlying}-INDEX"
            
            elif market_type == MarketType.FUTURES:
                if not expiry_date:
                    logger.warning(f"No expiry date provided for futures symbol {nse_symbol}")
                    return f"NSE:{underlying}FUT"
                
                # Extract year and month from expiry date
                year_month = self._format_expiry_for_futures(expiry_date)
                return f"NSE:{underlying}{year_month}FUT"
            
            elif market_type == MarketType.OPTIONS:
                if not expiry_date or not strike_price or not option_type:
                    logger.warning(f"Missing required fields for options symbol {nse_symbol}")
                    return f"NSE:{underlying}OPT"
                
                # Determine if it's weekly or monthly options
                is_weekly = self._is_weekly_option(expiry_date)
                
                if is_weekly:
                    # Weekly pattern: UNDERLYINGYYMDDSTRIKECE/PE
                    year_month_day = self._format_expiry_for_weekly_options(expiry_date)
                    strike_str = self._format_strike_price(strike_price)
                    return f"NSE:{underlying}{year_month_day}{strike_str}{option_type}"
                else:
                    # Monthly pattern: UNDERLYINGYYMMMSTRIKECE/PE
                    year_month = self._format_expiry_for_monthly_options(expiry_date)
                    strike_str = self._format_strike_price(strike_price)
                    return f"NSE:{underlying}{year_month}{strike_str}{option_type}"
            
            else:
                logger.warning(f"Unknown market type: {market_type}")
                return f"NSE:{underlying}"
                
        except Exception as e:
            logger.error(f"Error constructing Fyers symbol for {nse_symbol}: {e}")
            return f"NSE:{nse_symbol}"
    
    def _extract_underlying(self, symbol: str, market_type: MarketType) -> str:
        """Extract underlying symbol from NSE symbol."""
        # Remove common suffixes
        suffixes_to_remove = ['-EQ', '-INDEX', 'FUT', 'CE', 'PE']
        
        underlying = symbol
        for suffix in suffixes_to_remove:
            if underlying.endswith(suffix):
                underlying = underlying[:-len(suffix)]
                break
        
        # Remove year/month patterns for futures/options
        if market_type in [MarketType.FUTURES, MarketType.OPTIONS]:
            # Remove patterns like 25JUL, 2572, etc.
            underlying = re.sub(r'\d{2}[A-Z]{3}$', '', underlying)  # Remove 25JUL
            underlying = re.sub(r'\d{4}$', '', underlying)  # Remove 2572
            underlying = re.sub(r'\d+\.?\d*$', '', underlying)  # Remove strike prices
        
        return underlying.strip()
    
    def _format_expiry_for_futures(self, expiry_date: str) -> str:
        """Format expiry date for futures (YYMM format)."""
        try:
            dt = datetime.strptime(expiry_date, '%Y-%m-%d')
            year = str(dt.year)[-2:]  # Last 2 digits of year
            month_names = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN',
                          'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC']
            month = month_names[dt.month - 1]
            return f"{year}{month}"
        except Exception as e:
            logger.error(f"Error formatting expiry date {expiry_date}: {e}")
            return "25JUL"  # Default fallback
    
    def _format_expiry_for_monthly_options(self, expiry_date: str) -> str:
        """Format expiry date for monthly options (YYMM format)."""
        return self._format_expiry_for_futures(expiry_date)
    
    def _format_expiry_for_weekly_options(self, expiry_date: str) -> str:
        """Format expiry date for weekly options (YYMDD format)."""
        try:
            dt = datetime.strptime(expiry_date, '%Y-%m-%d')
            year = str(dt.year)[-2:]  # Last 2 digits of year
            month = str(dt.month)  # Single digit month
            day = f"{dt.day:02d}"  # Two digit day
            return f"{year}{month}{day}"
        except Exception as e:
            logger.error(f"Error formatting weekly expiry date {expiry_date}: {e}")
            return "25724"  # Default fallback
    
    def _is_weekly_option(self, expiry_date: str) -> bool:
        """Determine if an option is weekly based on expiry date."""
        try:
            dt = datetime.strptime(expiry_date, '%Y-%m-%d')
            # Weekly options typically expire on Thursdays
            # Monthly options typically expire on the last Thursday of the month
            
            # Simple heuristic: if it's not the last Thursday of the month, it's weekly
            # This is a simplified check - in practice, you'd need more sophisticated logic
            last_day = dt.replace(day=1, month=dt.month+1 if dt.month < 12 else 1, 
                                 year=dt.year if dt.month < 12 else dt.year+1) - timedelta(days=1)
            
            # Find last Thursday
            days_back = (last_day.weekday() - 3) % 7
            last_thursday = last_day - timedelta(days=days_back)
            
            return dt != last_thursday
        except Exception:
            return False  # Default to monthly if can't determine
    
    def _format_strike_price(self, strike_price: float) -> str:
        """Format strike price for options symbols."""
        # Remove decimal if it's a whole number
        if strike_price == int(strike_price):
            return str(int(strike_price))
        else:
            return str(strike_price).replace('.', '')
    
    def get_fyers_symbol_from_mapping(self, nse_symbol: str, market_type: MarketType) -> Optional[str]:
        """Get Fyers symbol from symbol_mapping table."""
        try:
            mapping = self.db.query(SymbolMapping).filter(
                and_(
                    SymbolMapping.nse_symbol == nse_symbol,
                    SymbolMapping.market_type == market_type,
                    SymbolMapping.is_active == True
                )
            ).first()
            
            if mapping:
                return mapping.fyers_symbol
            return None
            
        except Exception as e:
            logger.error(f"Error getting Fyers symbol from mapping: {e}")
            return None
    
    def update_fyers_symbols_in_ohlcv_tables(self) -> Dict[str, int]:
        """Update null fyers_symbol values in all OHLCV tables."""
        try:
            import psycopg2
            from src.core.config import settings
            
            db_url = settings.database.url
            conn = psycopg2.connect(db_url)
            
            update_counts = {
                'equity_ohlcv': 0,
                'index_ohlcv': 0,
                'futures_ohlcv': 0,
                'options_ohlcv': 0
            }
            
            try:
                with conn.cursor() as cursor:
                    # Update equity_ohlcv table - handle both symbol formats
                    cursor.execute("""
                        UPDATE equity_ohlcv
                        SET fyers_symbol = sm.fyers_symbol
                        FROM symbol_mapping sm
                        WHERE (
                            equity_ohlcv.symbol = sm.nse_symbol OR
                            equity_ohlcv.symbol = sm.fyers_symbol OR
                            equity_ohlcv.symbol = CONCAT('NSE:', sm.nse_symbol) OR
                            equity_ohlcv.symbol = CONCAT(sm.nse_symbol, '-EQ')
                        )
                        AND sm.market_type = 'EQUITY'
                        AND equity_ohlcv.fyers_symbol IS NULL
                        AND sm.is_active = true
                    """)
                    update_counts['equity_ohlcv'] = cursor.rowcount

                    # Update index_ohlcv table - handle both symbol formats
                    cursor.execute("""
                        UPDATE index_ohlcv
                        SET fyers_symbol = sm.fyers_symbol
                        FROM symbol_mapping sm
                        WHERE (
                            index_ohlcv.symbol = sm.nse_symbol OR
                            index_ohlcv.symbol = sm.fyers_symbol OR
                            index_ohlcv.symbol = CONCAT('NSE:', sm.nse_symbol) OR
                            index_ohlcv.symbol = CONCAT(sm.nse_symbol, '-INDEX')
                        )
                        AND sm.market_type = 'INDEX'
                        AND index_ohlcv.fyers_symbol IS NULL
                        AND sm.is_active = true
                    """)
                    update_counts['index_ohlcv'] = cursor.rowcount

                    # Update futures_ohlcv table - handle symbol formats
                    cursor.execute("""
                        UPDATE futures_ohlcv
                        SET fyers_symbol = sm.fyers_symbol
                        FROM symbol_mapping sm
                        WHERE (
                            futures_ohlcv.symbol = sm.nse_symbol OR
                            futures_ohlcv.symbol = sm.fyers_symbol OR
                            futures_ohlcv.symbol = CONCAT('NSE:', sm.nse_symbol)
                        )
                        AND sm.market_type = 'FUTURES'
                        AND futures_ohlcv.fyers_symbol IS NULL
                        AND sm.is_active = true
                    """)
                    update_counts['futures_ohlcv'] = cursor.rowcount

                    # Update options_ohlcv table - handle symbol formats
                    cursor.execute("""
                        UPDATE options_ohlcv
                        SET fyers_symbol = sm.fyers_symbol
                        FROM symbol_mapping sm
                        WHERE (
                            options_ohlcv.symbol = sm.nse_symbol OR
                            options_ohlcv.symbol = sm.fyers_symbol OR
                            options_ohlcv.symbol = CONCAT('NSE:', sm.nse_symbol)
                        )
                        AND sm.market_type = 'OPTIONS'
                        AND options_ohlcv.fyers_symbol IS NULL
                        AND sm.is_active = true
                    """)
                    update_counts['options_ohlcv'] = cursor.rowcount

                    conn.commit()
                    logger.info(f"Updated fyers_symbol values: {update_counts}")

            finally:
                conn.close()
                
            return update_counts

        except Exception as e:
            logger.error(f"Error updating fyers_symbols in OHLCV tables: {e}")
            return {}

    def fix_specific_symbols(self, test_symbols: Dict[str, str]) -> Dict[str, bool]:
        """
        Fix specific symbols with their exact Fyers symbol format.

        Args:
            test_symbols: Dict mapping market type to exact Fyers symbol

        Returns:
            Dict mapping market type to success status
        """
        results = {}

        for market_type_str, fyers_symbol in test_symbols.items():
            try:
                market_type = MarketType(market_type_str)

                # Extract base NSE symbol from Fyers symbol
                nse_symbol = self._extract_base_symbol_from_fyers(fyers_symbol, market_type)

                # Update or create symbol mapping
                success = self._update_symbol_mapping(nse_symbol, fyers_symbol, market_type)
                results[market_type_str] = success

                if success:
                    logger.info(f"✅ Fixed {market_type_str}: {nse_symbol} -> {fyers_symbol}")
                else:
                    logger.error(f"❌ Failed to fix {market_type_str}: {nse_symbol}")

            except Exception as e:
                logger.error(f"Error fixing symbol {market_type_str}: {e}")
                results[market_type_str] = False

        return results

    def _extract_base_symbol_from_fyers(self, fyers_symbol: str, market_type: MarketType) -> str:
        """
        Extract the base NSE symbol from Fyers symbol format.

        Examples:
        - NSE:RELIANCE-EQ -> RELIANCE
        - NSE:NIFTY50-INDEX -> NIFTY50
        - NSE:RELIANCE25JULFUT -> RELIANCE
        - NSE:NIFTY25JUL25000CE -> NIFTY
        """
        # Remove NSE: prefix
        symbol = fyers_symbol.replace('NSE:', '')

        if market_type == MarketType.EQUITY:
            # Remove -EQ suffix
            return symbol.replace('-EQ', '')
        elif market_type == MarketType.INDEX:
            # Remove -INDEX suffix
            return symbol.replace('-INDEX', '')
        elif market_type == MarketType.FUTURES:
            # Extract underlying from futures format (e.g., RELIANCE25JULFUT -> RELIANCE)
            # Look for pattern: SYMBOL + YYMMMFUT or SYMBOL + FUT
            import re
            # Match pattern like RELIANCE25JULFUT or RELIANCEFUT
            match = re.match(r'^([A-Z&-]+?)(?:\d{2}[A-Z]{3})?FUT$', symbol)
            if match:
                return match.group(1)
            # Fallback: remove common suffixes
            for suffix in ['FUT', '25JULFUT', '25AUGFUT', '25SEPFUT']:
                if symbol.endswith(suffix):
                    return symbol[:-len(suffix)]
            return symbol
        elif market_type == MarketType.OPTIONS:
            # Extract underlying from options format using consolidated utility
            from src.core.symbol_utils import SymbolUtils
            return SymbolUtils.extract_underlying_symbol(symbol, MarketType.OPTIONS)
        else:
            # Default: just remove NSE: prefix
            return symbol

    def _update_symbol_mapping(self, nse_symbol: str, fyers_symbol: str, market_type: MarketType) -> bool:
        """Update or create symbol mapping entry with duplicate prevention."""
        try:
            # Check if mapping exists by nse_symbol and market_type
            existing_by_nse = self.db.query(SymbolMapping).filter(
                and_(
                    SymbolMapping.nse_symbol == nse_symbol,
                    SymbolMapping.market_type == market_type
                )
            ).first()

            # Check if fyers_symbol already exists (should be unique)
            existing_by_fyers = self.db.query(SymbolMapping).filter(
                SymbolMapping.fyers_symbol == fyers_symbol
            ).first()

            if existing_by_fyers and existing_by_fyers != existing_by_nse:
                # Fyers symbol already exists for a different NSE symbol
                logger.warning(f"Fyers symbol {fyers_symbol} already exists for {existing_by_fyers.nse_symbol}")
                # Update the existing entry instead of creating duplicate
                existing_by_fyers.nse_symbol = nse_symbol
                existing_by_fyers.market_type = market_type
                existing_by_fyers.is_active = True
                existing_by_fyers.updated_at = datetime.now()
            elif existing_by_nse:
                # Update existing mapping by NSE symbol
                existing_by_nse.fyers_symbol = fyers_symbol
                existing_by_nse.is_active = True
                existing_by_nse.updated_at = datetime.now()
            else:
                # Create new mapping - only if both NSE and Fyers symbols are new
                new_mapping = SymbolMapping(
                    nse_symbol=nse_symbol,
                    fyers_symbol=fyers_symbol,
                    market_type=market_type,
                    exchange='NSE',
                    is_active=True
                )
                self.db.add(new_mapping)

            self.db.commit()
            return True

        except Exception as e:
            logger.error(f"Error updating symbol mapping: {e}")
            self.db.rollback()
            return False

    def validate_fyers_symbols(self) -> Dict[str, Any]:
        """Validate Fyers symbols in all tables."""
        validation_results = {
            'symbol_mapping_count': 0,
            'ohlcv_tables': {},
            'null_counts': {},
            'validation_passed': False
        }

        try:
            import psycopg2
            from src.core.config import settings

            db_url = settings.database.url
            conn = psycopg2.connect(db_url)

            try:
                with conn.cursor() as cursor:
                    # Check symbol_mapping table
                    cursor.execute("SELECT COUNT(*) FROM symbol_mapping WHERE is_active = true")
                    validation_results['symbol_mapping_count'] = cursor.fetchone()[0]

                    # Check each OHLCV table
                    tables = ['equity_ohlcv', 'index_ohlcv', 'futures_ohlcv', 'options_ohlcv']

                    for table in tables:
                        # Total records
                        cursor.execute(f"SELECT COUNT(*) FROM {table}")
                        total_count = cursor.fetchone()[0]

                        # Null fyers_symbol records
                        cursor.execute(f"SELECT COUNT(*) FROM {table} WHERE fyers_symbol IS NULL")
                        null_count = cursor.fetchone()[0]

                        # Non-null fyers_symbol records
                        cursor.execute(f"SELECT COUNT(*) FROM {table} WHERE fyers_symbol IS NOT NULL")
                        non_null_count = cursor.fetchone()[0]

                        validation_results['ohlcv_tables'][table] = {
                            'total_records': total_count,
                            'null_fyers_symbol': null_count,
                            'non_null_fyers_symbol': non_null_count,
                            'null_percentage': (null_count / total_count * 100) if total_count > 0 else 0
                        }

                        validation_results['null_counts'][table] = null_count

                    # Overall validation
                    total_nulls = sum(validation_results['null_counts'].values())
                    validation_results['validation_passed'] = total_nulls == 0

            finally:
                conn.close()

        except Exception as e:
            logger.error(f"Error validating Fyers symbols: {e}")

        return validation_results
