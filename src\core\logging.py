"""
Robust Data Storage Service Logging Configuration.
Production-ready logging with comprehensive error handling and monitoring.
"""

import logging
import logging.handlers
import sys
import os
import json
from typing import Optional, Dict, Any
from pathlib import Path
from datetime import datetime

# Configure logging formats
CONSOLE_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
FILE_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s"
JSON_FORMAT = {
    "timestamp": "%(asctime)s",
    "logger": "%(name)s",
    "level": "%(levelname)s",
    "function": "%(funcName)s",
    "line": "%(lineno)d",
    "message": "%(message)s"
}
DATE_FORMAT = "%Y-%m-%d %H:%M:%S"


class JSONFormatter(logging.Formatter):
    """Custom JSON formatter for structured logging."""

    def format(self, record):
        log_entry = {
            "timestamp": self.formatTime(record, self.datefmt),
            "logger": record.name,
            "level": record.levelname,
            "function": record.funcName,
            "line": record.lineno,
            "message": record.getMessage(),
            "module": record.module,
            "pathname": record.pathname
        }

        # Add exception info if present
        if record.exc_info:
            log_entry["exception"] = self.formatException(record.exc_info)

        # Add extra fields
        for key, value in record.__dict__.items():
            if key not in log_entry and not key.startswith('_'):
                log_entry[key] = value

        return json.dumps(log_entry)


class ContextFilter(logging.Filter):
    """Add context information to log records."""

    def filter(self, record):
        # Add process ID and thread ID
        record.pid = os.getpid()
        record.thread_id = record.thread

        # Add hostname if available
        try:
            import socket
            record.hostname = socket.gethostname()
        except:
            record.hostname = "unknown"

        return True


def setup_logging(
    level: str = "INFO",
    log_file: Optional[str] = None,
    console: bool = True,
    json_format: bool = False,
    max_bytes: int = 10 * 1024 * 1024,  # 10MB
    backup_count: int = 5
) -> None:
    """
    Setup comprehensive logging configuration.

    Args:
        level: Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: Optional log file path
        console: Whether to log to console
        json_format: Whether to use JSON formatting for file logs
        max_bytes: Maximum log file size before rotation
        backup_count: Number of backup files to keep
    """
    # Convert string level to logging constant
    numeric_level = getattr(logging, level.upper(), logging.INFO)

    # Get root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(numeric_level)

    # Clear existing handlers
    root_logger.handlers.clear()

    # Add context filter
    context_filter = ContextFilter()

    # Console handler with colored output
    if console:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(numeric_level)
        console_formatter = logging.Formatter(CONSOLE_FORMAT, DATE_FORMAT)
        console_handler.setFormatter(console_formatter)
        console_handler.addFilter(context_filter)
        root_logger.addHandler(console_handler)

    # File handler with rotation
    if log_file:
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)

        # Use rotating file handler
        file_handler = logging.handlers.RotatingFileHandler(
            log_file, maxBytes=max_bytes, backupCount=backup_count
        )
        file_handler.setLevel(numeric_level)

        # Choose formatter based on json_format flag
        if json_format:
            file_formatter = JSONFormatter()
        else:
            file_formatter = logging.Formatter(FILE_FORMAT, DATE_FORMAT)

        file_handler.setFormatter(file_formatter)
        file_handler.addFilter(context_filter)
        root_logger.addHandler(file_handler)

    # Error file handler for ERROR and CRITICAL logs
    if log_file:
        error_log_file = log_path.parent / f"{log_path.stem}_errors{log_path.suffix}"
        error_handler = logging.handlers.RotatingFileHandler(
            error_log_file, maxBytes=max_bytes, backupCount=backup_count
        )
        error_handler.setLevel(logging.ERROR)

        if json_format:
            error_formatter = JSONFormatter()
        else:
            error_formatter = logging.Formatter(FILE_FORMAT, DATE_FORMAT)

        error_handler.setFormatter(error_formatter)
        error_handler.addFilter(context_filter)
        root_logger.addHandler(error_handler)


def get_logger(name: str) -> logging.Logger:
    """
    Get a logger instance.

    Args:
        name: Logger name (usually __name__)

    Returns:
        Logger instance
    """
    # Setup logging if not already configured
    if not logging.getLogger().handlers:
        setup_logging()

    return logging.getLogger(name)


def log_performance(func):
    """Decorator to log function performance."""
    import functools
    import time

    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        logger = get_logger(func.__module__)
        start_time = time.time()

        try:
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            logger.debug(f"{func.__name__} executed in {execution_time:.4f} seconds")
            return result
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"{func.__name__} failed after {execution_time:.4f} seconds: {e}")
            raise

    return wrapper


def log_api_request(request_data: Dict[str, Any], response_data: Dict[str, Any] = None,
                   error: Exception = None) -> None:
    """Log API request and response data."""
    logger = get_logger("api")

    log_entry = {
        "request": request_data,
        "timestamp": datetime.now().isoformat()
    }

    if response_data:
        log_entry["response"] = response_data
        log_entry["status"] = "success"
        logger.info("API request completed", extra=log_entry)
    elif error:
        log_entry["error"] = str(error)
        log_entry["status"] = "error"
        logger.error("API request failed", extra=log_entry)


def log_database_operation(operation: str, table: str, records_affected: int = None,
                          error: Exception = None) -> None:
    """Log database operations."""
    logger = get_logger("database")

    log_entry = {
        "operation": operation,
        "table": table,
        "timestamp": datetime.now().isoformat()
    }

    if records_affected is not None:
        log_entry["records_affected"] = records_affected

    if error:
        log_entry["error"] = str(error)
        log_entry["status"] = "error"
        logger.error(f"Database {operation} failed", extra=log_entry)
    else:
        log_entry["status"] = "success"
        logger.info(f"Database {operation} completed", extra=log_entry)


def setup_production_logging(log_dir: str = "logs") -> None:
    """Setup production-ready logging configuration."""
    log_path = Path(log_dir)
    log_path.mkdir(exist_ok=True)

    # Main application log
    app_log = log_path / "app.log"

    # Setup with JSON format for production
    setup_logging(
        level="INFO",
        log_file=str(app_log),
        console=True,
        json_format=True,
        max_bytes=50 * 1024 * 1024,  # 50MB
        backup_count=10
    )

    # Setup specific loggers for different components
    loggers_config = {
        "database": logging.INFO,
        "api": logging.INFO,
        "fyers": logging.INFO,
        "data_storage": logging.INFO,
        "historical_data": logging.INFO,
        "symbol_service": logging.INFO,
        "uvicorn": logging.WARNING,
        "sqlalchemy": logging.WARNING
    }

    for logger_name, level in loggers_config.items():
        logger = logging.getLogger(logger_name)
        logger.setLevel(level)


# Enhanced production logging setup
def setup_enhanced_logging(log_dir: str = "logs") -> None:
    """Setup simplified logging with single daily file and clean console output."""
    from pathlib import Path
    import os
    from datetime import datetime

    # Create logs directory
    log_path = Path(log_dir)
    log_path.mkdir(exist_ok=True)

    # Single daily log file
    today = datetime.now().strftime("%Y-%m-%d")
    daily_log = log_path / f"dataservice_{today}.log"

    # Clear existing handlers
    root_logger = logging.getLogger()
    root_logger.handlers.clear()

    # Set root level
    root_logger.setLevel(logging.DEBUG)

    # Console handler with simplified format (no datetime)
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_format = logging.Formatter('%(levelname)-8s | %(message)s')
    console_handler.setFormatter(console_format)
    root_logger.addHandler(console_handler)

    # Single daily file handler with detailed format
    file_handler = logging.handlers.RotatingFileHandler(
        daily_log, maxBytes=100*1024*1024, backupCount=30, encoding='utf-8'
    )
    file_handler.setLevel(logging.DEBUG)
    file_format = logging.Formatter(
        '%(asctime)s | %(levelname)-8s | %(name)-20s | %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    file_handler.setFormatter(file_format)
    root_logger.addHandler(file_handler)

    # Configure specific loggers
    loggers_config = {
        "bulk_data_service": logging.INFO,
        "historical_data_service": logging.INFO,
        "symbol_service": logging.INFO,
        "data_storage_service": logging.INFO,
        "fyers_auth_service": logging.INFO,
        "symbol_downloader": logging.INFO,
        "database": logging.INFO,
        "api": logging.INFO,
        "uvicorn.access": logging.WARNING,
        "uvicorn.error": logging.INFO,
        # SQLAlchemy logging - suppress verbose ORM initialization logs
        "sqlalchemy.engine": logging.WARNING,  # SQL queries - only show warnings/errors
        "sqlalchemy.pool": logging.WARNING,    # Connection pool - only show warnings/errors
        "sqlalchemy.dialects": logging.WARNING,  # Dialect-specific logs - only show warnings/errors
        # Suppress noisy third-party loggers
        "urllib3.connectionpool": logging.WARNING,  # Reduce HTTP connection noise
        "urllib3.util.retry": logging.WARNING,     # Reduce retry noise
        "urllib3": logging.WARNING,  # General urllib3 logging
        "requests.packages.urllib3": logging.WARNING,  # Reduce requests noise
        "sqlalchemy.orm": logging.WARNING,       # ORM logs including mapper initialization - only show warnings/errors
        "sqlalchemy.orm.mapper": logging.WARNING,  # Specifically suppress mapper "initialize prop" logs
        "asyncio": logging.WARNING,  # Reduce asyncio noise
        # Application-specific loggers that should be less verbose
        "src.services.bulk_data_service": logging.INFO,
        "src.services.options_prioritizer": logging.INFO,
        "src.core.rate_limiter": logging.INFO,
    }

    for logger_name, level in loggers_config.items():
        logger = logging.getLogger(logger_name)
        logger.setLevel(level)

    # Simple startup message
    startup_logger = logging.getLogger("application.startup")
    startup_logger.info("🚀 Data Service - Project Goal: Real-time financial data storage and retrieval system")


# Setup default logging on import
try:
    from core.config import settings
    log_level = settings.log_level
except:
    log_level = "INFO"

# Use enhanced logging by default
setup_enhanced_logging()