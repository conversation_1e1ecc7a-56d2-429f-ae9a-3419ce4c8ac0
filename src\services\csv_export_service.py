"""
CSV Export Service for Fyers Symbols

This service provides functionality to export symbol processing results to CSV files
with different categories: All Fyers Symbol, Successful Fyers Symbol, Error Fyers Symbol, Active Symbols.
"""

import csv
import logging
import os
from datetime import datetime
from typing import Dict, List, Optional, Set
from pathlib import Path

from src.database.connection import get_db
from src.database.models import MarketType
from src.core.symbol_classifier import SymbolClassifier
from src.services.fyers_symbol_service import FyersSymbolService
from sqlalchemy.orm import Session
from sqlalchemy import text

logger = logging.getLogger(__name__)


class CSVExportService:
    """Service for exporting Fyers symbol data to CSV files."""
    
    def __init__(self, db_session: Optional[Session] = None):
        """Initialize the CSV export service."""
        self.db = db_session or next(get_db())
        self.symbol_classifier = SymbolClassifier()
        self.fyers_service = FyersSymbolService()
        self._setup_export_directory()
    
    def _setup_export_directory(self):
        """Setup export directory for CSV files."""
        try:
            self.export_dir = Path('exports/csv')
            self.export_dir.mkdir(parents=True, exist_ok=True)
            logger.info(f"CSV export directory setup: {self.export_dir}")
        except Exception as e:
            logger.error(f"Error setting up export directory: {e}")
            self.export_dir = Path('.')
    
    def export_symbols_for_market_type(
        self, 
        market_type: MarketType, 
        processing_results: Optional[Dict[str, bool]] = None,
        timestamp: Optional[str] = None
    ) -> str:
        """
        Export symbols for a specific market type to CSV.
        
        Args:
            market_type: Market type to export
            processing_results: Optional dict of symbol -> success status
            timestamp: Optional timestamp for filename
            
        Returns:
            Path to the exported CSV file
        """
        try:
            if not timestamp:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            filename = f"fyers_symbols_{market_type.value}_{timestamp}.csv"
            filepath = self.export_dir / filename
            
            # Get all symbols for this market type
            all_symbols = self._get_all_symbols_for_market_type(market_type)
            
            # Get active symbols (symbols with recent data)
            active_symbols = self._get_active_symbols_for_market_type(market_type)
            
            # Determine successful and error symbols
            successful_symbols = set()
            error_symbols = set()
            
            if processing_results:
                for symbol, success in processing_results.items():
                    if success:
                        successful_symbols.add(symbol)
                    else:
                        error_symbols.add(symbol)
            
            # Write CSV file
            with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)
                
                # Write header
                writer.writerow([
                    'All Fyers Symbol',
                    'Successful Fyers Symbol', 
                    'Error Fyers Symbol',
                    'Active Symbols'
                ])
                
                # Determine max length for rows
                max_length = max(
                    len(all_symbols),
                    len(successful_symbols),
                    len(error_symbols),
                    len(active_symbols)
                )
                
                # Convert sets to sorted lists
                all_symbols_list = sorted(list(all_symbols))
                successful_symbols_list = sorted(list(successful_symbols))
                error_symbols_list = sorted(list(error_symbols))
                active_symbols_list = sorted(list(active_symbols))
                
                # Write data rows
                for i in range(max_length):
                    row = [
                        all_symbols_list[i] if i < len(all_symbols_list) else '',
                        successful_symbols_list[i] if i < len(successful_symbols_list) else '',
                        error_symbols_list[i] if i < len(error_symbols_list) else '',
                        active_symbols_list[i] if i < len(active_symbols_list) else ''
                    ]
                    writer.writerow(row)
            
            logger.info(f"✅ Exported {market_type.value} symbols to: {filepath}")
            logger.info(f"   All symbols: {len(all_symbols)}")
            logger.info(f"   Successful: {len(successful_symbols)}")
            logger.info(f"   Errors: {len(error_symbols)}")
            logger.info(f"   Active: {len(active_symbols)}")
            
            return str(filepath)
            
        except Exception as e:
            logger.error(f"Error exporting symbols for {market_type.value}: {e}")
            return ""
    
    def _get_all_symbols_for_market_type(self, market_type: MarketType) -> Set[str]:
        """Get all symbols for a market type from the symbol mapping table."""
        try:
            all_symbols = set()

            # Get symbols from symbol_mapping table (which has fyers_symbol column)
            query = text("""
                SELECT DISTINCT fyers_symbol
                FROM symbol_mapping
                WHERE market_type = :market_type
                AND is_active = true
                AND fyers_symbol IS NOT NULL
            """)

            result = self.db.execute(query, {"market_type": market_type.value})
            for row in result:
                if row[0]:
                    all_symbols.add(row[0])

            return all_symbols

        except Exception as e:
            logger.error(f"Error getting all symbols for {market_type.value}: {e}")
            return set()
    
    def _get_active_symbols_for_market_type(self, market_type: MarketType) -> Set[str]:
        """Get active symbols (with recent data) for a market type."""
        try:
            active_symbols = set()

            # Map market type to OHLCV table
            table_mapping = {
                MarketType.EQUITY: 'equity_ohlcv',
                MarketType.INDEX: 'index_ohlcv',
                MarketType.FUTURES: 'futures_ohlcv',
                MarketType.OPTIONS: 'options_ohlcv'
            }

            table_name = table_mapping.get(market_type)
            if not table_name:
                return active_symbols

            # Get symbols with data in the last 30 days - simplified query
            # Note: OHLCV tables use 'datetime' column, not 'timestamp'
            try:
                query = text(f"""
                    SELECT DISTINCT fyers_symbol
                    FROM public.{table_name}
                    WHERE datetime >= CURRENT_DATE - INTERVAL '30 days'
                    AND fyers_symbol IS NOT NULL
                    LIMIT 1000
                """)

                result = self.db.execute(query)
                for row in result:
                    if row[0]:
                        active_symbols.add(row[0])

            except Exception as query_error:
                logger.debug(f"Query error for active symbols in {table_name}: {query_error}")
                # If query fails, return empty set - this is not critical for CSV export
                pass

            return active_symbols

        except Exception as e:
            logger.error(f"Error getting active symbols for {market_type.value}: {e}")
            return set()
    
    def export_all_market_types(
        self, 
        processing_results: Optional[Dict[MarketType, Dict[str, bool]]] = None
    ) -> Dict[MarketType, str]:
        """
        Export symbols for all market types.
        
        Args:
            processing_results: Optional nested dict of market_type -> symbol -> success status
            
        Returns:
            Dictionary mapping market types to their CSV file paths
        """
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            export_paths = {}
            
            for market_type in MarketType:
                # Get processing results for this market type
                market_results = None
                if processing_results and market_type in processing_results:
                    market_results = processing_results[market_type]
                
                # Export CSV for this market type
                csv_path = self.export_symbols_for_market_type(
                    market_type=market_type,
                    processing_results=market_results,
                    timestamp=timestamp
                )
                
                if csv_path:
                    export_paths[market_type] = csv_path
            
            logger.info(f"📊 Exported CSV files for {len(export_paths)} market types")
            return export_paths
            
        except Exception as e:
            logger.error(f"Error exporting all market types: {e}")
            return {}
